/**
 * Handles app user trade history data from polymarket. (May later be expanded to handle all polyuser trade history)
 */

import { defineStore } from 'pinia';
import { useApi } from 'src/api';
import localforage from 'localforage';
import { ApiHistoryItem, HistorySide, PolyDataHistory, PolyGammaHistoryItem, Serialized } from '@shared/api-dataclasses-shared';

const queueHistoryDelay = 10000;
const api = useApi();
let timerHistoryUpdate = -1;

export const useUserHistory = defineStore('userHistory', {
  state: () => ({
    historyByEventId: {} as unknown as Record<string, EventUserHistory>,
  }),
  getters: {
  },
  actions: {
    /**
     * //TODO: Tabled for now until the full app needs optimized.
     * Loads locally cached user history into store. (usually from indexedDb if browser supports)
     * @param eventId
     * @returns cached event history (or new if none exists)
     */
    async loadUserEventHistory(eventId: string): Promise<EventUserHistory> {
      //Remove if it exists
      this.clearUserEventHistory(eventId);

      const eventHistoryJson = await localforage.getItem(`history_${eventId}`) as Serialized<EventUserHistory> | null;
      const eventHistory = eventHistoryJson ? new EventUserHistory(eventHistoryJson) : new EventUserHistory(eventId);
      this.historyByEventId[eventId] = eventHistory;

      return eventHistory;
    },
    /**
     * Locally caches user event history (usually via indexedDb if browser supports)
     * @param eventHistory
     */
    async saveUserEventHistory(eventHistory: EventUserHistory | string) {
      if (typeof eventHistory === 'string') {
        eventHistory = this.historyByEventId[eventHistory];
        if (!eventHistory) {
          throw new Error(`Cannot save event history by unloaded id (${eventHistory}); load first before saving`);
        }
      }

      await localforage.setItem(`history_${eventHistory.eventId}`, eventHistory);
    },
    async fetchFullHistory(userProxyWallet: string, eventId: string, marketCondIds: string[]): Promise<void> {
      const history = await api.getFullUserHistory(userProxyWallet, marketCondIds); //should be sorted by time desc

      const eventHistory = new EventUserHistory(eventId);
      for (const historyItem of history) {
        eventHistory.addHistoryItem(historyItem, historyItem.condition_id);
      }

      this.historyByEventId[eventId] = eventHistory;
    },
    registerClientTrade(eventId: string, marketId: string, assetId: string, side: HistorySide, shares: number, price: number) {
      this.historyByEventId[eventId].addHistoryItem(new PolyDataHistory(assetId, price, shares, side, new Date(), true), marketId, true);
    },
    async clearUserEventHistory(eventId: string) {
      delete this.historyByEventId[eventId];
    },
    async clearAllHistory() {
      this.historyByEventId = {};
    },
    async queueFullHistoryFetch(userProxyWallet: string, eventId: string, marketCondIds: string[]) {
      if (timerHistoryUpdate !== -1) {
        clearTimeout(timerHistoryUpdate);
      }
      timerHistoryUpdate = setTimeout(async () => {
        await this.fetchFullHistory(userProxyWallet, eventId, marketCondIds);
        timerHistoryUpdate = -1;
      }, queueHistoryDelay) as unknown as number;
    }
  }
});

export class EventUserHistory {
  public eventId: string;
  public historyByCondId: Record<string, PolyDataHistory[]>;

  public constructor(eventId: string);
  public constructor(event: Serialized<EventUserHistory>);
  public constructor(event: string | Serialized<EventUserHistory>) {
    if (typeof event === 'string') {
      this.eventId = event;
      this.historyByCondId = {};
    }
    else {
      this.eventId = event.eventId;
      const historyLookup: Record<string, PolyDataHistory[]> = {};
      for (const marketId in event.historyByCondId) {
        const histories = event.historyByCondId[marketId].map((h) => new PolyDataHistory(h));
        historyLookup[marketId] = histories;
      }
      this.historyByCondId = historyLookup;
    }
  }

  public addHistoryItem(historyItem: ApiHistoryItem | PolyDataHistory, marketCondId: string, pushFrontToFront: boolean = false) {
    const historyArr = this.historyByCondId[marketCondId] ?? (this.historyByCondId[marketCondId] = []);

    if (!(historyItem instanceof PolyDataHistory)) {
      historyItem = new PolyDataHistory(historyItem);
    }

    //TODO: Push to back (front is inefficient) and just iterate backwards
    historyArr.splice(pushFrontToFront ? 0 : historyArr.length, 0, historyItem);
  }
}

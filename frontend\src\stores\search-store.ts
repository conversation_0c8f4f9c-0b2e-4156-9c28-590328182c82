import { defineStore } from "pinia";
import { useApi } from "src/api";
import { PolySearchResponse, PolyGammaEvent, PolySearchEventStatus, PolySearchEventSort, PolySearchEventFrequency } from "@shared/api-dataclasses-shared";

const api = useApi();

let shouldResetSearch = false;

export const useSearchStore = defineStore("search", {
  state: () => ({
    //Search data
    quickSearchResults: [] as PolyGammaEvent[],
    searchResults: [] as PolyGammaEvent[],
    isLastPage: false,

    //Loading and error states
    loading: false,
    quickSearchLoading: false,

    //Search parameters
    currentPage: 1,
    searchQuery: "",
    searchStatus: "active" as PolySearchEventStatus,

    //Search filters
    sortBy: "volume_24hr" as PolySearchEventSort,
    frequency: "all" as PolySearchEventFrequency,
    category: undefined as string | undefined,
    hideSports: false,
  }),

  getters: {
  },

  actions: {
    async quickSearchEvents(query: string, status: PolySearchEventStatus = "active"): Promise<PolyGammaEvent[]> {
      if (!query.trim()) {
        this.quickSearchResults = [];
        return [];
      }

      this.quickSearchLoading = true;

      try {
        const api = useApi();
        const response = await api.quickSearchEvents(query.trim(), status);
        this.quickSearchResults = response.events;
        return response.events;
      }
      finally {
        this.quickSearchLoading = false;
      }
    },

    async searchEvents(): Promise<void> {
      if (shouldResetSearch) {
        this.resetSearch();
        shouldResetSearch = false;
      }

      try {
        this.loading = true;
        const response = await api.searchEvents(
          this.searchQuery.trim(),
          this.currentPage,
          this.searchStatus,
          this.sortBy,
          this.frequency,
          this.category,
          this.hideSports
        );

        this.isLastPage = !response.hasMore;

        if (this.currentPage === 1) {
          //First page - replace results
          this.searchResults = response.events;
        } else {
          //Additional pages - append results
          this.searchResults.push(...response.events);
        }
      }
      finally {
        this.loading = false;
      }
    },

    async loadNextSearchPage(): Promise<void> {
      if (this.isLastPage || this.loading || !this.searchQuery.trim()) {
        return;
      }

      this.currentPage++;
      await this.searchEvents();
    },

    resetSearch(): void {
      this.currentPage = 1;
      this.searchResults = [];
    },

    setSearchFilters(filters: {
      query?: string;
      sortBy?: PolySearchEventSort;
      frequency?: PolySearchEventFrequency;
      searchStatus?: PolySearchEventStatus;
      category?: string;
      hideSports?: boolean;
    }): void {
      // Update any provided filters
      if (filters.query !== undefined) this.searchQuery = filters.query;
      if (filters.sortBy !== undefined) this.sortBy = filters.sortBy;
      if (filters.frequency !== undefined) this.frequency = filters.frequency;
      if (filters.searchStatus !== undefined) this.searchStatus = filters.searchStatus;
      if (filters.category !== undefined) this.category = filters.category;
      if (filters.hideSports !== undefined) this.hideSports = filters.hideSports;

      shouldResetSearch = true;
    },

    clearSearchFilters(): void {
      this.searchQuery = "";
      this.sortBy = "volume_24hr";
      this.frequency = "all";
      this.searchStatus = "active";
      this.category = undefined;
      this.hideSports = false;
      shouldResetSearch = true;
    },
  }
});

/// <reference lib="webworker" />

class PushNotifcationServiceWorker {
  constructor() {
    self.addEventListener('install', (event) => this.handleInstall(event as ExtendableEvent));
    self.addEventListener('activate', (event) => this.handleActivate(event as ExtendableEvent));
    self.addEventListener('push', (event) => this.handlePush(event as PushEvent));
  }

  private handleInstall(event: ExtendableEvent) {
    // This is where you can cache files, etc.
    event.waitUntil(
      (async () => {
        console.log('[Service Worker] Installing...');
        // Perform install steps (e.g., caching).
      })()
    );
  }

  private handleActivate(event: ExtendableEvent) {
    event.waitUntil(
      (async () => {
        console.log('[Service Worker] Activating...');
        // Cleanup old caches, etc.
      })()
    );
  }

  private handlePush(event: PushEvent) {
    if (!event.data) return;
    const data = event.data.json();
    event.waitUntil(
      (self as unknown as ServiceWorkerGlobalScope).registration.showNotification(data.title, {
        body: data.body,
        icon: data.icon || '/icons/favicon-96x96.png',
      })
    );
  }
}

new PushNotifcationServiceWorker();

import { ObjectSchema } from 'yup';
import { Request, Response, NextFunction } from 'express';
import { validate } from '@shared/validation-shared';
import { catchAsync } from '@src/utils';

declare global {
  namespace Express {
    export interface Response {
      sendValidationErrors(errors: Record<string, string>): Response;
    }
  }
}

function sendValidationErrors(this: Response, errors: Record<string, string>): Response {
  return this.status(400).send({ errors: errors });
}

export const validationExtensions = (/* options */) => {
    return (req: Request, res: Response, next: NextFunction) => {
    //Add extension methods to Response
    res.sendValidationErrors = sendValidationErrors;

    next();
  };
};

export const validation = (schema: ObjectSchema<any>) => {
  return catchAsync(async (req: Request, res: Response, next: NextFunction): Promise<void> => {

    const errors = await validate(schema, req.body);

    if (errors) {
      res.sendValidationErrors(errors);
    }
    else {
      next();
    }
  });
};

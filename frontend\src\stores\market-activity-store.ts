/**
 * Handles trade activity from the market's perspective rather than per user. (Think polymarket's activity feed)
 */

import { ApiMarketActivityItem } from '@shared/api-dataclasses-shared';
import { defineStore } from 'pinia';
import { useApi } from 'src/api';
import MarketActivityModel from 'src/models/market-activity-model';

const api = useApi();
let nextClientId = 1;

export const useMarketActivityStore = defineStore('marketActivity', {
  state: () => ({
    activityByCondId: {} as unknown as Record<string, MarketActivityModel[]>,
  }),
  getters: {
  },
  actions: {
    async fetchRecentActivity(marketCondIds: string[], intervalMs: number = 3600000) {
      const history = await api.getRecentMarketHistory(marketCondIds, intervalMs);

      for (const historyItem of history) {
        const storeActivity = this.activityByCondId[historyItem.condition_id] ?? (this.activityByCondId[historyItem.condition_id] = []);
        const modelItem = historyItem as MarketActivityModel;
        modelItem.clientId = nextClientId++;
        storeActivity.push(modelItem);
      }
    },

    async clearMarketActivity(marketCondId: string) {
      delete this.activityByCondId[marketCondId];
    },

    async clearAllActivity() {
      this.activityByCondId = {};
    },
  }
});

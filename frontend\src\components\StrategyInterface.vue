<template>
  <!-- Transition to make the footer slide up/down -->
  <transition name="slide-up-down">
    <q-footer unelevated fixed
      v-if="props.isShown"
      class="strategy-container"
    >
      <q-toolbar class="justify-center column">
        <div class="row strategy-info">
          <!-- Total best ask -->
          <div class="column items-center stat">
            <div class="text-caption text-grey-7">To Buy</div>
            <b>{{ formatCents(totalBestAsk) }}</b>
          </div>
          <!-- Total best bid -->
          <div class="column items-center stat">
            <div class="text-caption text-grey-7">To Sell</div>
            <b>{{ formatCents(totalBestBid) }}</b>
          </div>
          <!-- Total midpoint -->
          <div class="column items-center stat" title="Low range: midpoint when selling (round down). High range: midpoint when buying (round up).">
            <div class="text-caption text-grey-7">Midpoint</div>
            <b>{{ `${formatCents(totalMidpointSell)} - ${formatCents(totalMidpointBuy)}` }}</b>
          </div>

          <!-- Expand button -->
          <q-btn color="grey-7" class="btn-expand q-mr-md" dense no-caps
            @click="onClickExpandStrategies"
          >
            <q-icon :name="isExpanded ? 'keyboard_arrow_down' : 'keyboard_arrow_up'" />
            <span>Strategies</span>
          </q-btn>
          <!-- Invert selection -->
          <q-btn label="Invert" color="grey-7" class="q-mr-md" dense no-caps
            @click="onClickInvert"
          />
          <!-- Close -->
          <q-btn icon="close" flat
            @click="onClickClose"
          />
        </div>
        <div v-if="isExpanded" class="row strategy-form justify-center">
          <q-select label="Strategy" outlined
            v-model="selectedStrategy"
            :options="strategyOptions"
            class="q-ma-sm"
          />
        </div>
      </q-toolbar>
    </q-footer>
  </transition>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { PolyDataMarket } from "@shared/api-dataclasses-shared";
import { formatCents } from "src/utils";

const props = defineProps<{
  markets: PolyDataMarket[];
  isShown: boolean;
}>();
const emits = defineEmits(["close", "invertSelection"]);

//TODO: Consider making non-ref unless based on non-prop reactivity
const strategyOptions = ref([
  { label: 'Option 1', value: 'opt1' },
  { label: 'Option 2', value: 'opt2' },
  { label: 'Option 3', value: 'opt3' }
]);
const selectedStrategy = ref(strategyOptions.value[0]);
const isExpanded = ref(false);

//Watch shown status
watch(() => props.isShown, () => {
  isExpanded.value = false;
});

const totalBestAsk = computed(() => {
  return props.markets.reduce((acc, market) => acc + market.bestAskA, 0);
});

const totalBestBid = computed(() => {
  return props.markets.reduce((acc, market) => acc + market.bestBidA, 0);
});

const totalMidpointSell = computed(() => {
  return props.markets.reduce((acc, market) => acc + market.getMidpointA(false), 0);
});

const totalMidpointBuy = computed(() => {
  return props.markets.reduce((acc, market) => acc + market.getMidpointA(true), 0);
});

function onClickClose() {
  emits("close");
}

function onClickInvert() {
  emits("invertSelection");
}

function onClickExpandStrategies() {
  isExpanded.value = !isExpanded.value;
}

onMounted(() => {
});
onUnmounted(() => {
});
</script>

<style scoped>
.slide-up-down-enter-active,
.slide-up-down-leave-active {
  transition: transform 0.3s ease;
}
.slide-up-down-enter-from,
.slide-up-down-leave-to {
  transform: translateY(100%);
}

.strategy-container {
  background-color: white;
  border-top: 2px dashed #d5d5d5;
  color: black;

  .stat {
    padding-right: 20px;
  }

  :deep(.q-btn) {
    padding: 4px 10px 4px 10px;

    .q-icon {
      margin-left: -5px;
    }

    .q-icon:last-child {
      margin-left: 0px;
    }
  }
}

:deep(.q-toolbar) {
    padding: 0px;
    margin: 0px;
    max-width: none;
  }

.strategy-info {
  padding: 6px 0px 6px 0px;
}

.strategy-form {
  width: 100%;
  border-top: 1px solid #d5d5d5;
}
</style>

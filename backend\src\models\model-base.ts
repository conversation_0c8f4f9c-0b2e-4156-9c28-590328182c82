import { DBD<PERSON>, DB  } from '@src/dbTypes';
import { Updateable, Insertable, Selectable } from 'kysely';

type DTO<TTable extends keyof DBDTO> = DBDTO[TTable];

//Note: The "as any" casts aren't an issue as the type consistency is maintained in the constructor and class template
class ModelBase<TTable extends keyof DBDTO> {
  protected tableName: keyof DB;
  protected idColName: keyof TTable;

  constructor(tableName: keyof DB, idColName?: keyof TTable) {
    this.tableName = tableName;
    this.idColName = !idColName ? `${tableName}_id` as any : idColName;
  }

  /**
   * Find record by ID
   * @param id
   * @returns
   */
  public async findById(id: number): Promise<Selectable<TTable> | undefined> {
    const result = await global.db
      .selectFrom(this.tableName)
      .selectAll()
      .where(this.idColName as any, '=', id)
      .executeTakeFirst();

    return result as Selectable<TTable> | undefined;
  }

  public async findSingleBy<TCol extends keyof Selectable<TTable>>(col: TCol, value: Selectable<TTable>[TCol]): Promise<Selectable<TTable> | undefined> {
    const result = await global.db
      .selectFrom(this.tableName)
      .selectAll()
      .where(col as any, '=', value)
      .executeTakeFirst();

    return result as Selectable<TTable> | undefined;
  }

  /**
   * Create a new record
   * @param data
   * @returns
   */
  public async create(data: Insertable<TTable>): Promise<number> {
    const [result] = await global.db
      .insertInto(this.tableName)
      .values(data as any)
      .returning(this.idColName as any)
      .execute();

    return (result as any)[this.idColName as any] as number;
  }

  /**
   * Update an existing record by id
   * @param id
   * @param data
   */
  public async update(id: number, data: Updateable<TTable>): Promise<void> {
    console.log(id, data, this.tableName, this.idColName, "done");
    const [result] = await global.db
      .updateTable(this.tableName)
      .set(data as any)
      .where(this.idColName as any, '=', id)
      .execute();
  }

  /**
   * Delete an existing record by id
   * @param id
   */
  public async delete(id: number): Promise<void> {
    await global.db
      .deleteFrom(this.tableName)
      .where(this.idColName as any, '=', id)
      .execute();
  }
}

export default ModelBase;

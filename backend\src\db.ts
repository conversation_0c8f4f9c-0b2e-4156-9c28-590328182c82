declare global {
  var connectionPool: Pool;
  var db: Kysely<DB>;
  var appDataModel: AppDataModel;
  var polyUserHistoryModel: PolyUserHistoryModel;
  var polyUserModel: PolyUserModel;
  var marketModel: MarketModel;
  var marketHistoryModel: MarketHistoryModel;
}

import { Kysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';
import { DB } from '@src/dbTypes';
import AppDataModel, { AppData } from '@src/models/appdata-model';
import PolyUserHistoryModel from '@src/models/polyuser-history-model';
import PolyUserModel from '@src/models/polyuser-model';
import MarketModel from '@src/models/market-model';
import MarketHistoryModel from '@src/models/market-history-model';

export { DB };

export default () => {
  //TODO: Refactor this to be more like composable service pattern
  //Initialize globals used across app
  global.appDataModel = new AppDataModel();
  global.polyUserHistoryModel = new PolyUserHistoryModel();
  global.polyUserModel = new PolyUserModel();
  global.marketModel = new MarketModel();
  global.marketHistoryModel = new MarketHistoryModel();

  global.connectionPool = new Pool({
    connectionString: global.config.dbConnectionString,
  });

  global.db = new Kysely<DB>({
    dialect: new PostgresDialect({
      pool: connectionPool,
    }),
  });
};

<template>
  <div class="search-header-container">
    <div class="search-header-content">
      <!-- Left side: Sort and Frequency controls -->
      <div class="row items-center q-gutter-sm">
        <!-- Sort By Dropdown -->
        <div class="row items-center">
          <q-select label="Sort by" dense outlined class="sort-select"
            v-model="sortBy"
            :options="sortOptions"
            emit-value
          >
            <template v-slot:selected>
              <div class="row items-center no-wrap">
                <div
                  class="sort-icon q-mr-xs"
                  v-html="currentSortOption.icon"
                ></div>
                <span>{{ currentSortOption.label }}</span>
              </div>
            </template>
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <div
                    class="sort-icon"
                    v-html="scope.opt.icon"
                  ></div>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>

        <!-- Status Dropdown -->
        <div class="row items-center">
          <q-select label="Status" dense outlined class="status-select"
            v-model="searchStatus"
            :options="statusOptions"
            emit-value map-options
          />
        </div>

        <!-- Frequency Dropdown -->
        <div class="row items-center">
          <q-select label="Frequency" dense outlined class="frequency-select"
            v-model="frequency"
            :options="frequencyOptions"
            emit-value map-options
          />
        </div>

        <!-- Hide Sports Checkbox -->
        <div class="row items-center">
          <q-checkbox dense class="hide-sports-checkbox"
            v-model="hideSports"
            label="Hide sports markets"
          />
        </div>
      </div>

      <!-- Right side: Reserved space for categories (future implementation) -->
      <div class="row items-center">
        <!-- Categories will be implemented here later -->
        <div class="text-caption text-grey-6">
          <!-- Placeholder for category filters -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from 'vue';
import { useSearchStore } from 'src/stores/search-store';
import { PolySearchEventSort, PolySearchEventFrequency, PolySearchEventStatus, PolyGammaEvent } from '@shared/api-dataclasses-shared';

const searchStore = useSearchStore();

const sortBy = ref<PolySearchEventSort>(searchStore.sortBy);
const searchStatus = ref<PolySearchEventStatus>(searchStore.searchStatus);
const frequency = ref<PolySearchEventFrequency>(searchStore.frequency);
const hideSports = ref<boolean>(searchStore.hideSports);

const sortOptions = [
  {
    value: 'volume_24hr' as PolySearchEventSort,
    label: 'Trending',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>arrow trend up</title><g fill="currentColor"><path d="M1.75,12.25l3.646-3.646c.195-.195,.512-.195,.707,0l3.293,3.293c.195,.195,.512,.195,.707,0l6.146-6.146" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><polyline fill="none" points="11.25 5.75 16.25 5.75 16.25 10.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polyline></g></svg>`
  },
  {
    value: 'start_date' as PolySearchEventSort,
    label: 'Newest',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>pop</title><g fill="currentColor"><path d="m3.5417,3.6104l-1.7538,4.5807c-.1547.4042.1872.8238.6143.7539l4.8771-.7986c.4271-.0699.6173-.5768.3418-.9105l-3.1233-3.7821c-.2735-.3312-.8025-.2446-.9561.1566Z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="m14.25,12.5167h0c-1.274.9216-2.2147,2.2316-2.681,3.7333h0s0,0,0,0c-.9216-1.274-2.2316-2.2147-3.7333-2.681h0,0c1.274-.9216,2.2147-2.2316,2.681-3.7333h0s0,0,0,0c.9216,1.274,2.2316,2.2147,3.7333,2.681h0Z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><circle cx="13.5" cy="4.5" fill="none" r="2.75" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></circle></g></svg>`
  },
  {
    value: 'end_date' as PolySearchEventSort,
    label: 'Ending Soon',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>circle half dotted clock</title><g fill="currentColor"><polyline fill="none" points="9 4.75 9 9 12.25 11.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polyline><path d="M9,1.75c4.004,0,7.25,3.246,7.25,7.25s-3.246,7.25-7.25,7.25" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><circle cx="3.873" cy="14.127" fill="currentColor" r=".75" stroke="none"></circle><circle cx="1.75" cy="9" fill="currentColor" r=".75" stroke="none"></circle><circle cx="3.873" cy="3.873" fill="currentColor" r=".75" stroke="none"></circle><circle cx="6.226" cy="15.698" fill="currentColor" r=".75" stroke="none"></circle><circle cx="2.302" cy="11.774" fill="currentColor" r=".75" stroke="none"></circle><circle cx="2.302" cy="6.226" fill="currentColor" r=".75" stroke="none"></circle><circle cx="6.226" cy="2.302" fill="currentColor" r=".75" stroke="none"></circle></g></svg>`
  },
  {
    value: 'volume' as PolySearchEventSort,
    label: 'Total Volume',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>fire flame</title><g fill="currentColor"><path d="M6.962,16.25c-.28-2.75,1.803-2.097,1.875-4.501,1.581,.851,2.239,2.987,2.2,4.465" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="M11.037,16.214c3.901-1.516,4.725-5.833,1.964-9.85" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="M10.533,7.37s.696-3.766-2.044-5.62c-.364,4.375-5.109,4.531-5.109,9.237,0,2.117,1.096,4.402,3.582,5.263" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></g></svg>`
  },
  {
    value: 'liquidity' as PolySearchEventSort,
    label: 'Liquidity',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>droplet</title><g fill="currentColor"><path d="M9,16.25c3.038,0,5.5-2.47,5.5-5.517,0-4.191-3.083-5.983-5.5-8.983C6.583,4.75,3.5,6.542,3.5,10.733c0,3.047,2.462,5.517,5.5,5.517Z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="M9,13.75c-1.654,0-3-1.354-3-3.017" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></g></svg>`
  },
  {
    value: 'competitive' as PolySearchEventSort,
    label: 'Competitive',
    icon: `<svg height="18" width="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><title>boxing glove</title><g fill="currentColor"><path d="M13.25,13.75v1.5c0,.552-.448,1-1,1H6.75c-.552,0-1-.448-1-1v-1.5" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="M12.75,5.75c0,1.105-.895,2-2,2h-3.17" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path><path d="M4.018,6.751c-.17-.294-.268-.636-.268-1.001v-.5c0-1.657,1.343-3,3-3h5.5c1.657,0,3,1.343,3,3v5.5c0,1.657-1.343,3-3,3H5.25c-1.657,0-3-1.343-3-3v-2c0-1.105,.895-2,2-2h1.75c.966,0,1.75,.784,1.75,1.75h0c0,.967-.784,1.75-1.75,1.75h-1.25" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path></g></svg>`
  }
];

const frequencyOptions = [
  { value: 'all' as const, label: 'All' },
  { value: 'daily' as PolySearchEventFrequency, label: 'Daily' },
  { value: 'weekly' as PolySearchEventFrequency, label: 'Weekly' },
  { value: 'monthly' as PolySearchEventFrequency, label: 'Monthly' }
];

const statusOptions = [
  { value: 'active' as PolySearchEventStatus, label: 'Active' },
  { value: 'resolved' as PolySearchEventStatus, label: 'Resolved' },
  { value: 'all' as PolySearchEventStatus, label: 'All' }
];

//Computed properties for current selections
const currentSortOption = computed(() =>
  sortOptions.find(option => option.value === sortBy.value) || sortOptions[0]
);

//Update store filters when dropdowns change
watch(
  [sortBy, frequency, searchStatus, hideSports],
  async (newValues, oldValues) => {
    searchStore.setSearchFilters({
      sortBy: newValues[0],
      frequency: newValues[1],
      searchStatus: newValues[2],
      hideSports: newValues[3],
    });
    await searchStore.searchEvents();
  }
);

onUnmounted(() => {
  searchStore.clearSearchFilters();
});
</script>

<style scoped>
.search-header-container {
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  width: 100%;
}

.search-header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-select,
.status-select,
.frequency-select {
  min-width: 140px;
}

.sort-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-icon :deep(svg) {
  width: 18px;
  height: 18px;
  color: currentColor;
}

.hide-sports-checkbox {
  color: #424242;
}

.hide-sports-checkbox :deep(.q-checkbox__label) {
  color: #424242;
}

/* Style dropdowns for white background */
.sort-select :deep(.q-field__control),
.status-select :deep(.q-field__control),
.frequency-select :deep(.q-field__control) {
  background-color: white;
  color: #424242;
  border: 1px solid #e0e0e0;
}

.sort-select :deep(.q-field__native),
.status-select :deep(.q-field__native),
.frequency-select :deep(.q-field__native) {
  color: #424242;
}

.sort-select :deep(.q-icon),
.status-select :deep(.q-icon),
.frequency-select :deep(.q-icon) {
  color: #424242;
}

/* Style labels */
.text-caption {
  color: #757575 !important;
}


</style>

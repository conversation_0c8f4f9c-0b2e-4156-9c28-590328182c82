<template>
  <q-toolbar class="bg-primary header-ext">
    <q-toolbar-title class="text-center">
      <q-img v-if="props.imageUrl.trim() !== ''" :src="imageUrl" class="header-ext-image" fit="cover" />
      <a v-if="eventSlug" :href="`http://polymarket.com/event/${eventSlug}`" target="_blank" rel="noopener noreferrer" class="text-white q-ml-xs no-underline">
        <strong>{{ titleText }}</strong>
        <q-icon name="open_in_new" size="16px" />
      </a>
    </q-toolbar-title>
  </q-toolbar>
  <q-toolbar class="header-ext header-stats">
    <q-toolbar-title class="row justify-center">
      <!-- Tweet Count -->
      <div v-if="props.tweetCount !== -1" class="column items-center cursor-pointer" :title="`${formatTimeAgoShort(props.tweetLastChange)} ago`"
        @click.stop="onClickTweetCount"
      >
        <div class="text-caption text-grey-7">Tweet #</div>
        <div><a class="text-bold text-no underline-hover">{{ props.tweetCount }}</a></div>
      </div>

      <!-- PnL -->
      <div class="column items-center" :title="`Total rPnl: ${formatCurrency(props.realizedPnl, 0)}`">
        <div class="text-caption text-grey-7">PnL</div>
        <div class="flex items-center">
          <span :class="{'text-yes': props.lowPnl >= 0, 'text-no': props.lowPnl < 0}">{{ formatCurrency(props.lowPnl, 0) }}</span>
          &nbsp;<span class="text-mini">to</span>&nbsp;
          <span :class="{'text-yes': props.highPnl >= 0, 'text-no': props.highPnl < 0}">{{ formatCurrency(props.highPnl, 0) }}</span>
        </div>
      </div>

      <!-- Balance -->
      <div class="column items-center">
        <div class="text-caption text-grey-7">Balance</div>
        <div class="text-yes">
          {{ formatCurrency(user.balance) }}
        </div>
      </div>

      <!-- Buy/Sell Display toggle -->
      <div class="column items-center" title="Show buy or sell prices for markets">
        <div class="text-caption text-grey-7">
          <span :class="!app.isBuyDisplay ? 'text-weight-bold' : ''">Sell</span>
          <span>/</span>
          <span :class="app.isBuyDisplay ? 'text-weight-bold' : ''">Buy</span>
        </div>
        <q-toggle
          v-model="app.isBuyDisplay"
          size="sm"
          :color="app.isBuyDisplay ? 'green' : 'red'" keep-color
        />
      </div>
    </q-toolbar-title>
  </q-toolbar>
  <TweetAnalyzer v-if="props.tweetCount !== -1" v-model:isShown="isAnalyzerShown" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { formatCurrency, formatTimeAgoShort } from 'src/utils';
import { useUserStore } from 'src/stores/user-store';
import { useAppStore } from 'src/stores/app-store';
import TweetAnalyzer from 'src/components/TweetAnalyzer.vue';

const props = defineProps({
  titleText: { type: String, required: true },
  imageUrl: { type: String, default: '' },
  lowPnl: { type: Number, default: 0 },
  highPnl: { type: Number, default: 0 },
  realizedPnl: { type: Number, default: 0 },
  tweetCount: { type: Number, default: -1 },
  tweetLastChange: { type: Date, default: new Date() },
  eventSlug: { type: String },
});

const user = useUserStore();
const app = useAppStore();

const isAnalyzerShown = ref(false);

function onClickTweetCount() {
  isAnalyzerShown.value = true;
}
</script>

<style scoped lang="scss">
.header-ext-image {
  width: 42px;
  height: 42px;
  margin-right: 8px;
}

.header-ext {
  margin: 0px;
  max-width: none;
}

.header-stats {
  border-bottom: 1px solid rgb(151, 151, 151);
  color: grey;
}

.header-stats > .q-toolbar__title > div {
  padding-left: 15px;
  padding-right: 15px;
}

::v-deep(.q-toggle__inner--falsy) .q-toggle__thumb:after {
  background-color: #ff5e5e;
}

.underline-hover {
  text-decoration: none;
}

.underline-hover:hover {
  text-decoration: underline;
}
</style>

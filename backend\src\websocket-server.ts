import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';

let wss: SocketIOServer;

export function initWebSocketServer(httpServer: HTTPServer): SocketIOServer {
  wss = new SocketIOServer(httpServer, {
    cors: {
      origin: '*',
    },
  });

  wss.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
    });
  });

  return wss;
}

export function getWSS(): SocketIOServer {
  return wss;
}
import { Insertable, Selectable, sql } from "kysely";
import ModelBase from "./model-base";
import { PolyClient } from "@src/polymarket-api";
import { PolyGammaHistoryItem, stringToHistorySide } from "@shared/api-dataclasses-shared";
import { delay } from "@src/utils";
import { LockSpace } from "./model-shared";

const historyFetchSize = 40;
const historyMergeThreshold = 0; //Merge history items with time diff >= threshold
const historyFetchDelay = 1000; //Won't fetch any more frequently than once per this many ms

export default class PolyUserHistoryModel extends ModelBase<"polyuser_history"> {
  public constructor() {
    super("polyuser_history");
  }

  public async getUserMarketHistory(userProxyWallet: string, marketCondIds: string[]) {
    //TODO: Fix this so it's never not lowercase (clientside fixes ig)
    userProxyWallet = userProxyWallet.toLowerCase();

    //First lookup market ids from market table
    const marketIds = await db
    .selectFrom('market')
    .select(['market_id', 'condition_id'])
    .where('condition_id', 'in', marketCondIds)
    .execute();

    //Lookup user id from user table
    const userId = await db
    .selectFrom('polyuser')
    .select(['polyuser_id'])
    .where('proxy_wallet', '=', userProxyWallet)
    .execute();

    if (marketIds.length === 0) {
      return [];
    }

    //Select history matching market ids
    const historyData = await db
    .selectFrom('polyuser_history')
    .selectAll()
    .orderBy('time', 'desc')
    .where('user_id', '=', userId[0].polyuser_id)
    .where('market_id', 'in', marketIds.map(m => m.market_id))
    .execute();

    //Add condition id onto history data and return
    let lookupMarketCondIdById: Record<number, string> = [];
    for (const marketId of marketIds) {
      lookupMarketCondIdById[marketId.market_id] = marketId.condition_id;
    }

    return historyData.map(h => ({ ...h, condition_id: lookupMarketCondIdById[h.market_id], time: h.time.getTime() }));
  }

  //TODO: IMPORTANT: If this method is called twice close within a second or so, duplicate data could be inserted due to poly api latency.
  //                 Solution would be to lock the method on a per session or per data (wallet + cond ids) basis.
  /**
   * Pulls latest user history from Poly (ie history items newer than the latest in the db) and inserts that data into the db.
   * @param userProxyWallet
   * @param marketCondIds
   */
  public async updateUserHistoryFromPoly(polyClient: PolyClient, userProxyWallet: string, marketCondIds: string[]) {
    await db.transaction().execute(async (trx) => {

    const userId = (await global.polyUserModel.getIdsOrCreate([userProxyWallet]))[0].polyuser_id;

    //Get lock for user wallet so we don't race condition with another call to this method
    //Lock is auto-released by transaction commit/rollback
    await sql`SELECT pg_advisory_lock(${LockSpace.POLYUSER},${userId})`.execute(trx);

    const marketIds = await global.marketModel.getIdsOrCreate(marketCondIds);

    let latest = 0;
    //Select latest history item for user
    latest = (await db
    .selectFrom('polyuser_history')
    .select((eb) => eb.fn.max('time').as('time'))
    .where('user_id', '=', userId)
    .where('market_id', 'in', marketIds.map(m => m.market_id))
    .executeTakeFirst())?.time?.getTime() ?? 0;

    latest += 1000; //Bump up a second to avoid updating the same item

    //Get latest history from Poly (initial fetch is small in case of no updates)
    //Note: Data is sorted by timestamp desc which is in seconds
    let historyData = await polyClient.getUserActivity(userProxyWallet, marketCondIds, 0, 10);
    let shouldContinue = true;
    let curHistoryItem = {} as PolyGammaHistoryItem;
    const historyItemsToInsert = [];
    let lastFetchTime = Date.now();
    let offset = 0;
    while (shouldContinue && historyData.length > 0) {
      for (const h of historyData) {
        const hTimestamp = h.timestamp * 1000;
        if (hTimestamp  >= latest) {
          //Merge similar history items with same timestamp
          if (hTimestamp - curHistoryItem.timestamp <= historyMergeThreshold
           && curHistoryItem.type === h.type
           && curHistoryItem.asset === h.asset
           && curHistoryItem.price == h.price
          ) {
            curHistoryItem.size += h.size;
          }
          else {
            curHistoryItem = h;
            historyItemsToInsert.push(curHistoryItem);
          }
        }
        else {
          //Stop if we hit an older item
          shouldContinue = false;
          break;
        }
      }

      if (shouldContinue) {
        //Fetch more history
        const delayMs = Math.max(0, historyFetchDelay - (Date.now() - lastFetchTime));
        if (delayMs)
          await delay(delayMs);
        offset += historyData.length;
        historyData = await polyClient.getUserActivity(userProxyWallet, marketCondIds, offset, historyFetchSize);
        lastFetchTime = Date.now();
      }
    }

    if (historyItemsToInsert.length > 0) {
      //Insert historyItemsToInsert into db
      const insertHistoryData = historyItemsToInsert.map(h => ({
        user_id: userId,
        market_id: marketIds.find(m => m.condition_id === h.conditionId)!.market_id,
        asset_id: h.asset,
        side: stringToHistorySide(h.type === "TRADE" ? h.side : h.type) as number,
        time: new Date(h.timestamp * 1000),
        shares: h.size,
        price: h.price,
      }));

      await db.insertInto('polyuser_history')
      .values(insertHistoryData)
      .execute();
    }

    }); //end trx
  }
}

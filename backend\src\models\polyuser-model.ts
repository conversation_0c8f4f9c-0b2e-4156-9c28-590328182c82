import { Insertable, Selectable } from "kysely";
import ModelBase from "./model-base";

const historyFetchSize = 40;
const historyMergeThreshold = 0; //Merge history items with time diff >= threshold
const historyFetchDelay = 1000; //Won't fetch any more frequently than once per this many ms

export default class PolyUserModel extends ModelBase<"polyuser"> {
  public constructor() {
    super("polyuser");
  }

  //TODO: Make a discrete registration step (possibly on client init) so more user data can be registered and less spammy db calls
  public async getIdsOrCreate(userProxyWallets: string[]) {
    userProxyWallets = userProxyWallets.map(wallet => wallet.toLowerCase());
    //Lookup user id from user table
    let userIds = (await db
    .selectFrom('polyuser')
    .select(['polyuser_id', 'proxy_wallet'])
    .where('proxy_wallet', 'in', userProxyWallets)
    .execute());

    const missingUserIds = userProxyWallets.filter(wallet => userIds.findIndex(ui => wallet === ui.proxy_wallet) === -1);
    if (missingUserIds.length > 0) {
      const insertUserData = missingUserIds.map(wallet => ({ proxy_wallet: wallet  }));
      const inserted = await db.insertInto('polyuser')
      .values(insertUserData)
      .returning(['polyuser_id', 'proxy_wallet'])
      .execute();

      //Update our marketIds result set
      userIds = userIds.concat(inserted);
    }

    return userIds;
  }
}

import { HistorySide, PolyDataEvent, PolyDataHistory, PolyDataMarket, PolyDataPosition } from '@shared/api-dataclasses-shared';
import { computed, ComputedRef, proxyRefs, reactive, Ref, shallowReactive } from 'vue';

export default class MarketPnLModel {
  public readonly market: PolyDataMarket;
  public readonly totalBuys: number;
  public readonly totalSells: number;
  public readonly realizedPnl: number;
  public readonly lowEstimatePnl: number;
  public readonly highEstimatePnl: number;
  /**
   * The total pnl if this market resolves Yes. (Considers all markets in events and their positions/history as well)
   */
  public readonly yesPnl: number;
  public readonly noPnl: number;

  public constructor(marketRef: Ref<PolyDataMarket>, historyRef: Ref<PolyDataHistory[]>, allMarketPnlRef: Ref<MarketPnLModel[]>) {
    const totalBuys = computed(() => {
      if (!historyRef.value) return 0;
      return historyRef.value.reduce((acc, h) => acc + (h.side == HistorySide.Buy ? h.shares * h.price : 0), 0);
    });

    const totalSells = computed(() => {
      if (!historyRef.value) return 0;
      return historyRef.value.reduce((acc, h) => acc + (h.side == HistorySide.Sell ? h.shares * h.price : 0), 0);
    });

    const realizedPnl = computed(() => {
      return totalSells.value - totalBuys.value
    });

    const highEstimatePnl = computed(() => {
      return realizedPnl.value + (marketRef.value.positionA?.shares || 0) * marketRef.value.bestBidA + (marketRef.value.positionB?.shares || 0) * marketRef.value.bestBidB;
    });

    const lowEstimatePnl = computed(() => {
      //NOTE: This function assumes the book is already sorted by price descending
      const market = marketRef.value;
      if (!market.positionA && !market.positionB) return realizedPnl.value;

      let position = market.positionA || market.positionB;
      let totalSaleAllPositions = 0;
      while (position) {
        //Get shares sale sample size (25% of total shares)
        const sampleShares = Math.max(position.shares * 0.25, Math.min(100, position.shares));
        //Get the average price of the sample shares
        const sellTotal = market.simulateMarketSell(position.assetId, sampleShares);
        const avgSamplePrice = (sellTotal / sampleShares) || 0;
        totalSaleAllPositions += position.shares * avgSamplePrice;

        if (position !== market.positionB) {
          position = market.positionB;
        }
        else {
          break;
        }
      }

      return realizedPnl.value + totalSaleAllPositions;
    });

    //Whole event Pnl if this market resolves "Yes"
    const yesPnl = computed(() => {
      const market = marketRef.value;
      if (!allMarketPnlRef.value || !market.positionA) return 0;
      //Count our shares as gain if our position is Yes
      const ourGain = market.positionA.shares;
      return ourGain + allMarketPnlRef.value.reduce((acc, marketPnl) => {
        acc += marketPnl.realizedPnl;
        //Add No shares from other positions
        if (marketPnl.market != market && marketPnl.market.positionB) {
          acc += marketPnl.market.positionB.shares;
        }
        return acc;
      }, 0);
    });

    //Market Pnl if this market resolves "No"
    const noPnl = computed(() => {
      if (!marketRef.value.positionB) return 0;
      return marketRef.value.positionB.shares + realizedPnl.value;
    });

    //Proxy these so properties can be accessed without .value which avoids the unwrapping issues causing runtime vs typescript mismatches within bindings and computeds.
    this.market = proxyRefs(marketRef) as any;
    this.totalBuys = shallowReactive(totalBuys) as any;
    this.totalSells = shallowReactive(totalSells) as any;
    this.realizedPnl = shallowReactive(realizedPnl) as any;
    this.highEstimatePnl = shallowReactive(highEstimatePnl) as any;
    this.lowEstimatePnl = shallowReactive(lowEstimatePnl) as any;
    this.yesPnl = shallowReactive(yesPnl) as any;
    this.noPnl = shallowReactive(noPnl) as any;
  }
}

import { Insertable, Selectable } from "kysely";
import ModelBase from "./model-base";

const historyFetchSize = 40;
const historyMergeThreshold = 0; //Merge history items with time diff >= threshold
const historyFetchDelay = 1000; //Won't fetch any more frequently than once per this many ms

export default class MarketModel extends ModelBase<"market"> {
  public constructor() {
    super("market");
  }

  public async getIds(marketCondIds: string[]) {
    let marketIds = await db
    .selectFrom('market')
    .select(['market_id', 'condition_id'])
    .where('condition_id', 'in', marketCondIds)
    .execute();

    return marketIds;
  }

  //TODO: Make a discrete registration step (possibly on client init) so more market data can be registered and less spammy db calls
  public async getIdsOrCreate(marketCondIds: string[]) {
    //First lookup market ids from market table
    let marketIds = await db
    .selectFrom('market')
    .select(['market_id', 'condition_id'])
    .where('condition_id', 'in', marketCondIds)
    .execute();

    //If some markets not found, insert missing into db
    const missingMarketIds = marketCondIds.filter(m => marketIds.findIndex(mi => mi.condition_id === m) === -1);
    if (missingMarketIds.length > 0) {
      const insertMarketData = missingMarketIds.map(m => ({ condition_id: m  }));
      const inserted = await db.insertInto('market')
      .values(insertMarketData)
      .returning(['market_id', 'condition_id'])
      .execute();

      //Update our marketIds result set
      marketIds = marketIds.concat(inserted);
    }

    return marketIds;
  }
}

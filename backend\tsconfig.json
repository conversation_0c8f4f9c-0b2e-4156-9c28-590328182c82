{
  "compilerOptions": {
    //"traceResolution": true,
    /* Projects */
    "incremental": true,
    /* Language and Environment */
    "target": "es2016",
    /* Modules */
    "module": "commonjs",
    "baseUrl": "./",
    "paths": {
      "@shared/*": ["../shared/*"],
      "@synced-validation/*": ["src/validation/*"],
      "@src/*": ["src/*"]
    },
    "rootDirs": [
      "./src",
      "../shared"
    ],
    "outDir": "./bin",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "sourceMap": true,
  },
  "include": [
    "src/**/*",
    "../shared/**/*"
  ],
  "exclude": [
    "node_modules"
  ]
}

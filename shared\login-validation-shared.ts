
import * as yup from 'yup';
import { isServer, ServerStringSchema, ClientStringSchema } from '@synced-validation/custom-validators';
//import { test } from '@shared/custom-validators-shared';

let schemaRegisterDef: Record<string, yup.StringSchema> = {
  displayName: yup.string()
    .required()
    .min(4, 'Must be 4 or more characters')
    .max(20, 'Must be 20 or fewer characters')
    .matches(/^[a-zA-Z0-9 _-]*$/, 'Must not contain special characters')
  ,email: yup.string()
    .required()
    .email('Invalid email address')
  ,password: yup.string()
    .required()
    .min(8, 'Must be 8 or more characters')
    .matches(/[0-9]/, 'Must contain a number')
};

if (isServer) {
  schemaRegisterDef.displayName = (schemaRegisterDef.displayName as ServerStringSchema)
    .dbUnique('appuser', 'username', 'This name is already taken');

  schemaRegisterDef.email = (schemaRegisterDef.email as ServerStringSchema)
    .dbUnique('appuser', 'email', 'Email already registered!');
}
else {
  //Confirm password field
  schemaRegisterDef.passwordConfirm = yup.string()
    .required()
    .oneOf([yup.ref('password')], 'Passwords do not match');
}

export const schemaRegister = yup.object(schemaRegisterDef);

let schemaLoginDef: Record<string, yup.StringSchema> = {
  email: yup.string()
    .required()
    .email('Invalid email address')
  ,password: yup.string()
    .required()
}

export const schemaLogin = yup.object(schemaLoginDef);

let schemaVerifyEmailDef: Record<string, yup.StringSchema> = {
  token: yup.string()
    .required(),
}

export const schemaVerifyEmail = yup.object(schemaVerifyEmailDef);

let schemaForgotPasswordDef: Record<string, yup.StringSchema> = {
  email: yup.string()
    .required()
    .email('Invalid email address')
}

export const schemaForgotPassword = yup.object(schemaForgotPasswordDef);

let schemaResetPasswordDef: Record<string, yup.StringSchema> = {
  token: yup.string()
    .required(),
  password: schemaRegisterDef.password //Copy password requirements from registration schema
}

if (!isServer) {
  //Confirm password field
  schemaResetPasswordDef.passwordConfirm = yup.string()
    .required()
    .oneOf([yup.ref('password')], 'Passwords do not match');
}

export const schemaResetPassword = yup.object(schemaResetPasswordDef);

let schemaUserProfileDef: Record<string, yup.StringSchema> = {
  username: schemaRegisterDef.displayName, //Copy from registration schema
}

export const schemaUserProfile = yup.object(schemaUserProfileDef);

let schemaUserAccountDef: Record<string, yup.StringSchema> = {
  newEmail: schemaRegisterDef.email, //Copy from registration schema
}

export const schemaUserAccount = yup.object(schemaUserAccountDef);
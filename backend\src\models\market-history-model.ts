import { Insertable, Selectable } from "kysely";
import ModelBase from "./model-base";
import { PolyClient } from "@src/polymarket-api";
import { delay } from "@src/utils";
import { ApiMarketActivityItem, PolyDataTrade, PolyWSTrade, stringToHistorySide } from "@shared/api-dataclasses-shared";

const historyFetchSize = 40;
const historyMergeThreshold = 0; //Merge history items with time diff >= threshold
const historyFetchDelay = 1000; //Won't fetch any more frequently than once per this many ms

export default class MarketHistoryModel extends ModelBase<"market_history"> {
  public constructor() {
    super("market_history");
  }

  public async getRecentMarketHistory(marketCondIds: string[], timeToGoBackMs: number = 3600000) {
    if (timeToGoBackMs > 3600000) {
      throw new Error(`Time to go back must be <= than 1 hour (was ${timeToGoBackMs})`);
    }

    const marketIds = await global.marketModel.getIds(marketCondIds);

    const historyData = await db
    .selectFrom('market_history')
    .selectAll()
    .where('market_id', 'in', marketIds.map(m => m.market_id))
    .where('time', '>=', new Date(Date.now() - timeToGoBackMs))
    .execute();

    let lookupMarketCondIdById: Record<number, string> = [];
    for (const marketId of marketIds) {
      lookupMarketCondIdById[marketId.market_id] = marketId.condition_id;
    }

    return historyData.map(h => ({ ...h, condition_id: lookupMarketCondIdById[h.market_id], time: h.time.getTime() })) as ApiMarketActivityItem[];
  }

  public async updateRecentMarketHistory(polyClient: PolyClient, marketCondIds: string[]) {
    //This avoids fetching incomplete data as this method of updating is "patchy" instead of complete
    const fetchTimeToGoBackMs = 3600000;

    const marketIds = await global.marketModel.getIdsOrCreate(marketCondIds); //Add 1 second to avoid fetching the same data again

    const historyItemsToInsert: PolyDataTrade[] = [];
    for (const marketId of marketIds) {
      let lastFetchTime = Date.now();
      let latest = 0;
      //Select latest market history time
      latest = (await db
      .selectFrom('market_history')
      .select((eb) => eb.fn.max('time').as('time'))
      .where('market_id', '=', marketId.market_id)
      .executeTakeFirst())?.time?.getTime() ?? 0;

      latest = Math.max(latest + 1000, Date.now() - fetchTimeToGoBackMs);

      let historyData = await polyClient.getMarketActivity(marketId.condition_id, 0, 10);
      let shouldContinue = true;
      let curHistoryItem = {} as PolyDataTrade;
      let offset = 0;
      while (shouldContinue && historyData.length > 0) {
        for (const h of historyData) {
          const hTimestamp = h.timestamp * 1000;
          if (hTimestamp  >= latest) {
            curHistoryItem = h;
            historyItemsToInsert.push(curHistoryItem);
          }
          else {
            //Stop if we hit an older item
            shouldContinue = false;
            break;
          }
        }

        if (shouldContinue) {
          //Fetch more history
          const delayMs = Math.max(0, historyFetchDelay - (Date.now() - lastFetchTime));
          if (delayMs)
            await delay(delayMs);
          offset += historyData.length;
          historyData = await polyClient.getMarketActivity(marketId.condition_id, offset, historyFetchSize);
          lastFetchTime = Date.now();
        }
      }
    }

    if (historyItemsToInsert.length > 0) {
      //Insert new history items into db
      const insertHistoryData = historyItemsToInsert.map(h => ({
        user_proxy_wallet: h.proxyWallet,
        market_id: marketIds.find(m => m.condition_id === h.conditionId)!.market_id,
        asset_id: h.asset,
        side: stringToHistorySide(h.side) as number,
        time: new Date(h.timestamp * 1000),
        shares: h.size,
        price: h.price,
      }));

      await db.insertInto('market_history')
      .values(insertHistoryData)
      .execute();
    }
  }
}

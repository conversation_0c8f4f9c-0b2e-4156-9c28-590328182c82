import { Notify, QNotifyCreateOptions } from 'quasar';

let _notifier: Notifier | null = null;

class Notifier {
  public success(msg: string, options?: Partial<QNotifyCreateOptions>) {
    let opts: QNotifyCreateOptions = {
      progress: true, //TODO: Make this pause on hover
      message: msg,
      type: 'positive',
      color: 'green',
      icon: 'check_circle',
      position: 'bottom',
      classes: 'text-h6',
      timeout: 3000
    };
    if (options) {
      opts = { ...opts, ...options };
    }
    Notify.create(opts);
  }

  public error(msg: string, options?: Partial<QNotifyCreateOptions>) {
    let opts: QNotifyCreateOptions = {
      progress: true,
      message: msg,
      type: 'negative',
      color: 'red',
      icon: 'error',
      position: 'bottom',
      classes: 'text-h6',
      timeout: 4000
    };
    if (options) {
      opts = { ...opts, ...options };
    }
    Notify.create(opts);
  }

  public warn(msg: string, options?: Partial<QNotifyCreateOptions>) {
    let opts: QNotifyCreateOptions = {
      progress: true,
      message: msg,
      type: 'warning',
      color: 'orange',
      icon: 'warning',
      position: 'bottom',
      classes: 'text-h6',
      timeout: 4000
    };
    if (options) {
      opts = { ...opts, ...options };
    }
    Notify.create(opts);
  }
}

export function useNotifier() {
  return _notifier ?? (_notifier = new Notifier());
}

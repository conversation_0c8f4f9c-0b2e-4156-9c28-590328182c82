{
  "extends": "@quasar/app-vite/tsconfig-preset",
  "compilerOptions": {
    "baseUrl": "./",
    "lib": ["esnext", "dom", "WebWorker"],
    //"types": [],
    "paths": {
      "src/*": ["src/*"],
      "app/*": ["*"],
      "components/*": ["src/components/*"],
      "layouts/*": ["src/layouts/*"],
      "pages/*": ["src/pages/*"],
      "assets/*": ["src/assets/*"],
      "boot/*": ["src/boot/*"],
      "stores/*": ["src/stores/*"],
      "validation/*": ["src/validation/*"],
      "@shared/*": ["../shared/*"],
      "@synced-validation/*": ["src/validation/*"],
    },
  }
}

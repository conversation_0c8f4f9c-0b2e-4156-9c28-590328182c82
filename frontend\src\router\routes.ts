import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/IndexPage.vue') },
      { path: 'events/:slug', component: () => import('pages/EventPage.vue') },
      { path: 'search', component: () => import('pages/SearchPage.vue') },
      // { path: 'register', component: () => import('pages/LoginPage.vue'), props: { isSignUp: true } },
    ],
  },
  //Should be last, 404
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;

/*  EXAMPLE OF HOW TO LAYOUT ROUTES

import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      { path: '', component: () => import('pages/IndexPage.vue') },
      // Additional child routes within the MainLayout
      { path: 'about', component: () => import('pages/AboutPage.vue') },
      { path: 'contact', component: () => import('pages/ContactPage.vue') },
    ],
  },
  // Example of another main route with a different layout
  {
    path: '/admin',
    component: () => import('layouts/AdminLayout.vue'),
    children: [
      { path: '', component: () => import('pages/admin/AdminHomePage.vue') },
      { path: 'users', component: () => import('pages/admin/UsersPage.vue') },
      { path: 'settings', component: () => import('pages/admin/SettingsPage.vue') },
    ],
  },
  // Catch-all route should remain last
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;


*/

import { Selectable } from "kysely";
import ModelBase from "./model-base";
import { AppdataDTO } from "@src/dbTypes";

export default class AppDataModel extends ModelBase<"appdata"> {
  public constructor() {
    super("appdata");
  }

  public async getAppData(): Promise<AppData> {
    const result = await global.db
    .selectFrom("appdata")
    .selectAll()
    .execute();

    const appData: AppData = new AppData(result);

    //TODO: Maybe some validation for critical app data values if they're not present

    return appData;
  }

  public async updateAppData(name: string, value: string) {
    await db.insertInto('appdata')
    .values({ name, value })
    .onConflict((oc) => oc.column('name').doUpdateSet({ value }))
    .execute();
  }
}

export class AppData {
  lastElonTweetCount: number = 0;
  lastElonTweetTime: Date = new Date();

  constructor(dbData: Selectable<AppdataDTO>[]) {
    for (const data of dbData) {
      if (data.name === 'lastElonTweetCount') {
        this.lastElonTweetCount = parseInt(data.value || '0');
      }
      else if (data.name === 'lastElonTweetTime') {
        this.lastElonTweetTime = data.value ? new Date(data.value) : new Date();
      }
    }
  }
}

import { ObjectSchema, ValidationError } from 'yup';

export type ValidationErrors = Record<string, string>;

export const validate = async (schema: ObjectSchema<any>, data: Record<string, string>): Promise<ValidationErrors | false> => {
  let errors: ValidationErrors = {};
  let hasErrors: boolean = false;
  
  for (const fieldName in data) {
    try {
      await schema.validateAt(fieldName, data);
    }
    catch (err) {
      if (err instanceof ValidationError) {
        hasErrors = true;
        //console.log("%O", err);
        //Collect validation errors into key value store
        if (err.path)
          errors[err.path!] = err.message;
        else
          throw `validation error with no path (fieldName) '${err.message}'`;
      }
    }
  }

  return hasErrors ? errors : false;
}
//Configure these as needed
const polyApiUrl = 'https://clob.polymarket.com';
const privateKey = '';
const walletAddress = '';
const proxyUrl = 'http://dJKlsbanYv7qJJF:BTub6GPOzzORDhR@************:43287';
const rpcUrl = 'https://polygon-rpc.com';
const testUrl = 'http://api.ipify.org/?format=json';

process.env.HTTP_PROXY = proxyUrl;
process.env.HTTPS_PROXY = proxyUrl;
const ethers = require('ethers');
const { ClobClient } = require('@polymarket/clob-client');

async function run()
{
    //const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey);

    console.log("signer", signer);
    console.log('Wallet Base Address:', signer.address);

    const clobClient = new ClobClient(polyApiUrl, 137, signer);

    const apiKeys = await clobClient.createOrDeriveApiKey(0);//nonce = Math.floor(Math.random() * 1000000).toString();
    console.log(apiKeys);
    console.log('API Key:', apiKeys.key);
    console.log('Secret:', apiKeys.secret);
    console.log('Passphrase:', apiKeys.passphrase);
}

// USDC Contract Address on Polygon
const usdcContractAddress = "******************************************";

// ERC-20 ABI (Minimal version for balance lookup)
const erc20Abi = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)"
];

async function getBalances() {
    const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);
  // Native MATIC Balance
  const nativeBalance = await provider.getBalance(walletAddress);
  console.log("💰 Native MATIC Balance:", ethers.utils.formatEther(nativeBalance));

  // ERC-20 Token Balance (USDC)
  const usdcContract = new ethers.Contract(usdcContractAddress, erc20Abi, provider);

  const usdcBalance = await usdcContract.balanceOf(walletAddress);
  const decimals = await usdcContract.decimals();
  const formattedUsdcBalance = ethers.utils.formatUnits(usdcBalance, decimals);

  console.log("💵 USDC Balance:", formattedUsdcBalance);
}

run();

import * as fs from 'fs';
import * as yup from 'yup';
const requireJson = require('require-json5');

//Add new appconfig vars here
const configSchema = yup.object({
  serverPort: yup.number().required().default(3000),
  clientUrl: yup.string().required().default('http://localhost:3000'),
  url: yup.string().required().default('http://localhost:3000'),
  dbConnectionString: yup.string().required().default('postgres://postgres:test@localhost/PolyEnhanced'),
  privateKey: yup.string().required().default(''),
  polyProxy: yup.string().default(''),
  polyProxyWallet: yup.string().required().default(''),
  polyBaseWallet: yup.string().required().default(''),
  polyCredsApiKey: yup.string().required().default(''),
  polyCredsSecret: yup.string().required().default(''),
  polyCredsPassphrase: yup.string().required().default(''),
  polyClobSignatureType: yup.number().required()
    .oneOf([0, 1, 2], 'polyClobSignatureType must be 0 (EOA), 1 (POLY_PROXY eg magic.link wallets), or 2 (POLY_GNOSIS_SAFE eg brave wallet, meta wallet, etc)')
    .default(0),
  polyClobApi: yup.string().required().default('https://clob.polymarket.com'),
  polyGammaApi: yup.string().required().default('https://gamma-api.polymarket.com'),
  polyDataApi: yup.string().required().default('https://data-api.polymarket.com'),
  tweetCounterPollRateMs: yup.number().required().default(60000),
});

export type ConfigObject = yup.InferType<typeof configSchema>;

/**
 * Parses and validates the config at configPath. Creates a default config if it doesn't exist. Exits process if invalid.
 * @param configPath - Path to the config file
 * @returns Parsed and validated config object
 */
export function loadConfig(configPath: string) : ConfigObject {
  let config: any;

  try {
    config = requireJson(configPath);
  } catch (error: any) {
    //If file not found, create it
    if (error.code === 'ENOENT') {
      const defaultConfig = configSchema.getDefault();
      fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2), 'utf8');
      config = defaultConfig;
      console.log(`Config file not found at ${configPath}. Creating default config...`);
    } else {
      //Re-throw any other errors
      throw error;
    }
  }

  //Validate appconfig file, throw exception on failure
  try {
    //Validate present fields (strict to ignore defaults and also "transformations")
    configSchema.validateSync(config, { abortEarly: false, strict: true });
  }
  catch (err) {
    if (err instanceof yup.ValidationError) {
      console.error('Invalid app config file:');
      err.inner.forEach(innerError => {
        console.error(`Error with property '${innerError.path!}': ${innerError.message}`);
      });

      process.exit(-1);
    }
    else {
      throw err;
    }
  }

  return config;
};

export default loadConfig;

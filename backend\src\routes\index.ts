import express, { NextFunction, Request, Response } from 'express';
import { catchAsync, enableAxiosProxy, getPolyClient } from '@src/utils';
import { HttpsProxyAgent } from 'https-proxy-agent';
import WebSocket from 'ws';
import axios from 'axios';

const router = express.Router();

const testTakerAddr = '';
const testMarketCondId = '0xfff59ecac0ae27bd47e78fd241cc4847c4d0ab801796bc8b8d801906690a683c';
let wsTest: WebSocket | null = null;

router.get('/test2', catchAsync(async (req: Request, res: Response) => {

  // enableAxiosProxy(true);

  // const signer = new Wallet(config.privateKey);
  // const client = new ClobClient(config.polyClobApi, 137, signer, creds);

  // const data = await client.getTrades({ market: testMarketCondId });
  // console.log(testTakerAddr, data);

  // enableAxiosProxy(false);

  // res.send(data);

  if (wsTest) {
    wsTest.close();
    wsTest = null;
  }

  wsTest = new WebSocket('wss://ws-subscriptions-clob.polymarket.com/ws/market', { agent: new HttpsProxyAgent(config.polyProxy) });

  wsTest.on('open', () => {
    console.log('WebSocket connected');

    const payload = JSON.stringify({
      //auth: { apiKey: config.polyCredsKey, secret: config.polyCredsSecret, passphrase: config.polyCredsPassphrase },
      type: 'market',
      assets_ids: ['78097908165041251548503313086371007618528449856318404320362601869169613172571'], // Replace with correct asset IDs
    });
    console.log(payload);
    wsTest!.send(payload);
  });

  wsTest.on('message', (data) => {
    console.log('Received:', data);
  });

  wsTest.on('error', (error) => {
    console.error('WebSocket Error:', error);
    wsTest = null;
  });

  wsTest.on('close', (code, reason) => {
    console.log(`WebSocket disconnected. Code: ${code}, Reason: ${reason.toString()}`);
    wsTest = null;
  });

  res.send('Done');

}));

router.get('/test3', catchAsync(async (req: Request, res: Response) => {
  const response = await axios({
    method: 'POST',
    url: 'https://gamma-api.polymarket.com/comments',
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'en-US,en;q=0.9',
      'content-type': 'application/json',
      'priority': 'u=1, i',
      'sec-ch-ua': '"Not(A:Brand";v="99", "Brave";v="133", "Chromium";v="133"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'sec-gpc': '1',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
      'Referrer-Policy': 'no-referrer',
      "cookie": "polymarketnonce=MTczOTEyMTE4NXxjT3lWVmhyWVl2WU9iUk1EY3lvMFM2VWxNUnJfWW42VTF3UndCV0lGWHdQZG9kTDRjTGctT09FVHZTY1VDTHVZbXphcXZHME5mUFY4eDEtYWFMemNPREpJaE9BSUkzeWlqa04zTzdQajB4V1k2UExwd1B2OUVnPT18T7WUtACSeKR4Zv_QOgkzRpUGTHiUt0_mRfzs5kO31qY=; polymarketsession=MTczOTEyMTE5N3xPMVF4U0V5U2RsSjhjcG9iQUN0RTlHSi1RZTZRQzhPVFM2aFcwLXRLdlR0UDNvaHNFYzNXMkFSNXdFWHR4S1lIaTEzaklzenJCRk5QdVVsbTdNQ1QzWHF2NkVwRHloVmpKLXBHTVdQLTUzQVVBVlF4QjQwT0dtS1ZYeVl0cjJMUmVCeUZhOWc1eGVfemJfQ3BCMWh0TG9LSHNwaUZtTzJFYUFjRWlobnBjN3RzelVMUTBtODhXRmhiSm5jRXZpMEwzWUppaWJBbWZOMWpaSjZZa0F6VHZNbUlxM09IakxsWDd3Nm41NHZ0a0NwUDhZM0pOX1Q3YlRQWEh3TmE4bS1hWHNadlZkZ2pySUh2YTJWS0Qybll6aVE1aXpXT0poTnk2Vm5DRk5xbTFNQTd0S2plWWE3b25YcEVfVmFKZ1hjNk5CYTg1MXN4R0R5anhTTmRuV0JjN045YWVPYnV1ZDJDdkV2ek9WWjlJQzJwMVVZNVVpc1RxcmYwdTZiOV8zOWp1NjBMdnZMTjVEcGpVb1hwUFlsWWllMTVza3R4b1F5ZFgxSzk0RG1hdkhvdHlMS0RhcHBMWF85d1JYTENuU1ZtbkNMNk8yaV83MTRvc3NVYnRWVmdHMWJ3Q3d6eHZCWnZjRWQ0VUM3Y0ViTHJtX245WTU2RjI0RHlkbjRNMV9mSU9zZnR3MmlyYXIxOU0zY0FNa3ZhT0x6Q2ZuTDRhVzhEbW4ycnQweTNXZV9TZGNDcWZnTnZrcWM0QmxaUVhsNzJ4S0xSUnNsYzVRdjVfelRQNGYwRWlHVWdkbVk4R2w2X2pDbHRvaTJtR3pMVWwzMUo1UnFfWEVsaVUtRnlHMUF3Qmc2ejJyako1Y2o2RmNFMTAtMGFnNm5DaGlhVndEVnRpRDR6THNMeW9sSjVDZGRwODlaTEZMZ1FHU3ZYd2ZNOGJjSW1KcWpHN3BTZVZpV1hDTDhoUnI0TmdDSV9EamNPLVRkS3k4WW1FaEhEYXBXUTBJX1dzb2I1N243cnpUNXZhWVMxYU1CNHpwb0xhVm1FMmlESDZUYmY1VjRQd1pwdUZ6N1VsVzNnbXBGWjVWSXU0RVlRYVNNYXZCcERCQ1NGWnZGQ0RvTlNpNlFMNTRaVGt3ZEZMZi1nTDJIMlJlSmVrZzdyUVozSHZ6SHhXby1YVFBlaDJJTmE2dlNVM01CdXBLZFJ4NUpuQk9xLU5qQ0FUaDlaMXhtNFJyTXV4R25VcFhUYWlFcE1CZDI3eFhyMnpTeE84TU9lVUhDSWhTdW9IYnZJOFlBVTlCU1lTRXY4azc5aE9GU0JhSHZxR0EyQnFHN0dxUlNIdm56bFp0M0NqYVZLYWtDSTM5MEx3NjY4MnFkblFydkRodUV6VjdiazRRVGJFTHR1WUtNd3N1dHppQ2ZOZ2oydVgxQWdHZzhFRGE5WVI1VGVkMlBLY2tNT1I1RWt2RHowTGV6ajV2dmRZTkNWTElNTmlXQ2w1aUZBfLXGQaJqWg1N4fBmrgHO86cWuLpX41n-ChcHWrVk3X0C; polymarketauthtype=magic;",
    },
    // The request body can be an object; Axios will JSON-stringify it for you
    data: {
      body: req.query.msg || 'TEST',
      parentEntityId: 10000,
      parentEntityType: 'Series',
      parentCommentId: '1022892',
      //replyAddress: '******************************************'
    }
    // referrerPolicy is a Fetch API property and isn't directly supported by Axios
    // If needed, you can manually handle referrers or proxies here.
  });

  res.send(response.data);
}));

router.get('/testerror', catchAsync(async (req: Request, res: Response) => {
  res.status(500).send({ error: 'Here\' the error msg' });
}));

//DEBUG: Remove this after proper "profile" page or something similar
router.get('/pks', catchAsync(async (req: Request, res: Response) => {
  //Get data from config
  res.send({
    walletAddress: config.polyProxyWallet,
    baseAddress: config.polyBaseWallet,
    secret: config.polyCredsSecret,
    passphrase: config.polyCredsPassphrase,
    apiKey: config.polyCredsApiKey,
  });
}));

export default router;

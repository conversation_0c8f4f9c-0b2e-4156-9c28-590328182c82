<template>
  <div
    class="row items-center q-pa-sm bg-white hover:bg-grey-2 cursor-pointer main-container no-select"
    @click="onClickExpand" v-bind="$attrs"
  >
    <!-- Market name -->
    <div class="col text-h6">
      <span class="market-name" @click.stop="onClickResolutionInfo">{{ marketData.shortName || "Resolution Info" }}</span>
      <q-menu v-model="showResolutionInfo" anchor="top middle" self="bottom middle" class="menu-resolution">
        <div v-html="resolutionText"></div>
      </q-menu>
    </div>

    <!-- Open orders -->
    <div class="col-auto text-left q-pr-sm" style="width: 165px">
      <div v-for="order in props.orderData!" class="text-order">
        <b>{{ `${order.isBuy ? 'BUY' : 'SELL'} ${props.marketData.lookupAssetName(order.assetId)} ` }}</b>
        <span>{{ `${formatDecimal(order.sharesMatched, 0, false)}/${formatDecimal(order.sharesTotal, 0, false)}` }}</span>
        <span class="float-right">{{ `${formatCents(order.price)}` }}</span>
      </div>
    </div>

    <!-- Position/Pnl info -->
    <div class="col-auto text-left" style="width: 140px">
      <!-- shares -->
      <div v-if="props.marketData.positionA" class="text-outcome-yes">
          <b>{{ `${props.marketData.positionA.outcome} ${formatDecimal(props.marketData.positionA.shares, 2, true)}` }}</b><span>{{ ` @ ${formatCents(props.marketData.positionA.avgPrice, 1)}` }}</span>
      </div>
      <div v-if="props.marketData.positionB" class="text-outcome-no">
          <b>{{ `${props.marketData.positionB.outcome} ${formatDecimal(props.marketData.positionB.shares, 2, true)}` }}</b><span>{{ ` @ ${formatCents(props.marketData.positionB.avgPrice, 1)}` }}</span>
      </div>
      <!-- Realized Pnl -->
      <div v-if="props.pnlData && (props.historyData?.length || 0)" class="text-rpnl" :title="`realized pnl: -${formatCurrency(props.pnlData.totalBuys, 0)}(buys) + ${formatCurrency(props.pnlData.totalSells, 0)}(sells) = ${formatCurrency(props.pnlData.realizedPnl, 0)}`">
        <span class="text-mini">rPnL</span>&nbsp;
        <b>{{ formatCurrency(props.pnlData.realizedPnl, 0) }}</b>
      </div>
      <!-- Estimated Pnl -->
      <div v-if="props.pnlData && props.marketData.hasPosition()" title="Estimated current pnl: high is selling at best bid, low is avg price of market selling 25% (capped) of position.">
        <span class="text-mini">PnL</span>&nbsp;
        <span :class="props.pnlData.lowEstimatePnl >= 0 ? 'text-green' : 'text-red'">{{ formatCurrency(props.pnlData.lowEstimatePnl, 0) }}</span>
        &nbsp;<span class="text-mini">to</span>&nbsp;
        <span :class="props.pnlData.highEstimatePnl >= 0 ? 'text-green' : 'text-red'">{{ formatCurrency(props.pnlData.highEstimatePnl, 0) }}</span>
      </div>
      <div v-if="props.pnlData" class="text-rpnl flex items-center">
        <!-- On Yes Pnl -->
        <div v-if="props.marketData.positionA" title="Whole event Pnl if this market resolves Yes">
          <span class="text-mini">yPnl&nbsp;</span>
          <span :class="props.pnlData.yesPnl >= 0 ? 'text-green' : 'text-red'">{{ formatCurrency(props.pnlData.yesPnl, 0) }}</span>&nbsp;
        </div>
        <!-- On No Pnl -->
        <div v-if="props.marketData.positionB" title="Market's pnl if this market resolves No">
          &nbsp;<span class="text-mini">nPnl&nbsp;</span>
          <span :class="props.pnlData.noPnl >= 0 ? 'text-green' : 'text-red'">{{ `${(props.pnlData.noPnl >= 0 ? '+' : '')}${formatCurrency(props.pnlData.noPnl, 0)}` }}</span>
        </div>
      </div>
    </div>

    <!-- Chance % -->
    <div class="col-auto text-h5 flex justify-center" style="width: 100px">
      {{ formatDecimal((props.marketData.bestAskA / (props.marketData.bestAskA + props.marketData.bestAskB)) * 100, 1, true) }}%
    </div>

    <!-- Outcomes + prices -->
    <div class="col-auto" style="width: 190px">
      <q-btn-group unelevated class="btn-group-buysell">
        <q-btn toggle no-caps dense
          :label="`${marketData.outcomeNameA} ${formatDecimal(priceA * 100, 1, true)}¢`"
          @click.stop="onClickOutcomeA"
          class="outcome btn-yes" :class="{ 'btn-selected': isOutcomeASelected }"
        />
        <q-btn toggle no-caps dense
          :label="`${marketData.outcomeNameB} ${formatDecimal(priceB * 100, 1, true)}¢`"
          @click.stop="onClickOutcomeB"
          class="outcome btn-no" :class="{ 'btn-selected': isOutcomeBSelected }"
        />
      </q-btn-group>
      <div class="flex justify-evenly">
        <div class="flex justify-center">
          <div class="stat-mini-label">
            Spread:&nbsp;
          </div>
          <div class="stat-mini">
            {{ formatDecimal(marketData.spread! * 100, 1, true) }}¢
          </div>
        </div>
        <div class="flex justify-center">
          <div class="stat-mini-label">
            Vol:&nbsp;
          </div>
          <div class="stat-mini">
            {{ formatCurrency(marketData.volume!, 0) }}
          </div>
        </div>
      </div>
    </div>
  </div>
  <MarketOrderInterface v-if="props.isExpanded"
    :event-id="props.eventId"
    :market-data="marketData"
    :order-data="orderData"
    :history-data="historyData"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PolyDataHistory, PolyDataMarket, PolyDataOpenOrder } from '@shared/api-dataclasses-shared';
import { formatCurrency, formatCents, formatDecimal, roundDecimal, linkifyText } from 'src/utils';
import MarketOrderInterface from 'src/components/MarketOrderInterface.vue';
import MarketPnLModel from 'src/models/market-pnl-model';

const props = defineProps<{
  eventId: string;
  marketData: PolyDataMarket;
  orderData?: PolyDataOpenOrder[];
  isExpanded?: boolean;
  isOutcomeASelected?: boolean;
  isOutcomeBSelected?: boolean;
  isBuyDisplay?: boolean;
  historyData?: PolyDataHistory[];
  pnlData?: MarketPnLModel;
}>();

const emits = defineEmits(["click", "clickOutcomeA", "clickOutcomeB"]);

const showResolutionInfo = ref(false);

const resolutionText = computed(() => {
  return linkifyText(props.marketData.resolution || "No resolution text available.");
});

const priceA = computed(() => {
  return props.isBuyDisplay ? props.marketData.bestAskA : props.marketData.bestBidA;
});

const priceB = computed(() => {
  return props.isBuyDisplay ? props.marketData.bestAskB : props.marketData.bestBidB;
});

function onClickExpand() {
  emits("click");
}

function onClickOutcomeA() {
  emits("clickOutcomeA");
}

function onClickOutcomeB() {
  emits("clickOutcomeB");
}

function onClickResolutionInfo() {
  //Popup resolution info tooltip
  showResolutionInfo.value = !showResolutionInfo.value;
}
</script>

<style scoped lang="scss">
.main-container {
  border-top: 1px solid #cdcdcd;
  font-family: OpenSauceSans;
  font-size: 12px;
}

.market-name {
  text-decoration: underline dotted;
  text-decoration-color: grey;
  text-decoration-thickness: 1px;
  cursor: help;
}

.info-icon {
  color: grey;
  cursor: help;
  opacity: 0.7;
}

.outcome {
  width: 95px;
  font-weight: bold;
  font-size: medium;
  padding: 4px 0px 4px 0px;
}

.outcome:nth-child(even) {
  background-color: $NoBackground;
  color: $NoPrimary;
  border-radius: 0 6px 6px 0;
}

.outcome:nth-child(odd) {
  background-color: $YesBackground;
  color: $YesPrimary;
  border-radius: 6px 0 0 6px;
}

.stat-mini {
  font-size: 10px;
  color: #bfbfbf;
  display: flex;
  align-items: center;
}

.stat-mini-label {
  font-size: 8px;
  color: #bfbfbf;
  display: flex;
  align-items: center;
}

.text-outcome-yes {
  color: $YesPrimary;
  background-color: $YesBackground;
}

.text-outcome-no {
  color: $NoPrimary;
  background-color: $NoBackground;
}

.text-win {
  color: rgb(59, 160, 0);
}

.text-lose {
  color: rgb(138, 0, 0);
}

.text-rpnl {
  font-size: 11px;
  color: #b1b1b1;
}

.text-order {
  color: $OrderPrimary;
  background-color: $OrderBackground;
}

:deep(.btn-selected) {
  &.btn-yes {
    background-color: $YesPrimary;
    color: white;
  }

  &.btn-no {
    background-color: $NoPrimary;
    color: white;
  }
}
</style>

<style lang="scss">
.menu-resolution {
  border-radius: 6px;
  padding: 8px;
  max-width: 800px;
  white-space: pre-line;
}
</style>

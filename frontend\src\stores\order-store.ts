import { defineStore } from 'pinia';
import { useApi } from 'src/api';
import { PolyDataOpenOrder, PolyClobOpenOrder, PolyWSOrder, PolyPostOrderResponse, PolyCancelOrderResponse } from '@shared/api-dataclasses-shared';

const api = useApi();

export const useOrderStore = defineStore('order', {
  state: () => ({
    ordersByCondId: {} as unknown as Record<string, StoreOrders>,
    ordersById: {} as unknown as Record<string, PolyDataOpenOrder>,
  }),
  getters: {
  },
  actions: {
    /**
     * Fetches ALL open orders from the api into the store
     */
    async fetchOrders(): Promise<void> {
      this.clearOrders();

      const orders = await api.getOrders();
      for (let i = 0; i < orders.length; i++) {
        const order = orders[i]
        this.registerOrder(PolyDataOpenOrder.fromApiJson(order));
      }
    },

    registerOrder(order: PolyDataOpenOrder): void {
      const storeOrders = this.ordersByCondId[order.market] ?? (this.ordersByCondId[order.market] = new StoreOrders(order.market));
      storeOrders.addOrder(order);
      this.ordersById[order.id] = order;
    },

    /**
     * Removes order by id if it exists in the store.
     * @param orderId
     */
    removeOrder(orderId: string): void {
      const order = this.ordersById[orderId];
      if (order) {
        const storeOrders = this.ordersByCondId[order.market];
        storeOrders.removeOrder(orderId);
        if (storeOrders.orders.length === 0) {
          delete this.ordersByCondId[order.market];
        }
        delete this.ordersById[orderId];
      }
    },

    clearOrders(): void {
      this.ordersByCondId = {};
      this.ordersById = {};
    },

    /**
     * Update store state based on WS events UPDATE, PLACEMENT, and CANCELLATION.
     * @param wsOrder
     * @returns
     */
    processOrderWSEvent(wsOrder: PolyWSOrder): void {
      //console.log("Order event", wsOrder);

      let shouldRemove = false;
      if (wsOrder.type == "UPDATE") {
        //Get corresponding order matching event
        const order = this.ordersById[wsOrder.id];
        if (!order) {
          console.error("Order update event for non-registered order", wsOrder);
          return;
        }
        //Update order
        if (Number(wsOrder.original_size) - Number(wsOrder.size_matched) < 0.01) {
          //Remove order
          shouldRemove = true;
        }
        else {
          order.sharesMatched = Number(wsOrder.size_matched);
        }
      }
      else if (wsOrder.type == "PLACEMENT") {
        const newOrder = PolyDataOpenOrder.fromWSJson(wsOrder);
        //Remove existing order (if it exists, will do nothing otherwise)
        this.removeOrder(newOrder.id);
        //Add new order
        this.registerOrder(newOrder);
      }
      else if (wsOrder.type == "CANCELLATION") {
        //Remove order
        shouldRemove = true;
      }
      else {
        console.error("Unknown WebSocket order event type:", wsOrder.type, wsOrder);
      }

      if (shouldRemove) {
        //Remove order
        if (this.ordersById[wsOrder.id])
          this.removeOrder(wsOrder.id);
        else
          console.error("Order cancellation event for non-registered order", wsOrder);
      }
    },

    async placeOrder(assetId: string, price: number, shares: number, isBuy: boolean, tickSize: string): Promise<PolyPostOrderResponse> {
      price = Number(price.toFixed(3));
      return await api.placeOrder(assetId, price, shares, isBuy, tickSize);
    },

    async cancelOrder(orderIds: string[]): Promise<PolyCancelOrderResponse> {
      const res = await api.cancelOrders(orderIds);
      return res;
    },
  }
});

export class StoreOrders {
  public marketCondId: string;
  public orders: PolyDataOpenOrder[];

  public constructor(marketCondId: string) {
    this.marketCondId = marketCondId;
    this.orders = [];
  }

  public addOrder(order: PolyDataOpenOrder) {
    this.orders.push(order);
  }

  public removeOrder(orderId: string) {
    const index = this.orders.findIndex((order) => order.id === orderId);
    if (index !== -1) {
      this.orders.splice(index, 1);
    }
  }
}

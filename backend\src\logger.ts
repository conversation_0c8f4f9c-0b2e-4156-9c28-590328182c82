import morgan from 'morgan';
import rfs from 'rotating-file-stream';
import nodeUtil from 'util';

const config = global.config;

//Add logging to file for all console output
let logger: any;
if (process.env.NODE_ENV === 'production') {
  const pad = (num: number) => (num > 9 ? "" : "0") + num;
  const nameGenerator = (time: Date | number, index?: number) => {
    if (typeof time === 'number') {
      // Handle the case where time is a number.
      time = new Date(time);
    }

    if (!time) return "log.txt";

    var month = time.getFullYear() + "" + pad(time.getMonth() + 1);
    var day = pad(time.getDate());
    var hour = pad(time.getHours());
    var minute = pad(time.getMinutes());

    return `${month}${day}-${hour}${minute}-${index}-log.txt`;
  };

  const logStream = rfs.createStream(nameGenerator, {
    path: "logs",
    size: '5M', //rotate every x bytes written (M = MB, G = GB, etc)
    interval: '1d', //rotate daily
    compress: 'gzip', //compress rotated files
    maxFiles: 20,
  });

  const timeFormatOptions: any = { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
  const log = function (str: string) {
    logStream.write(`${new Date().toLocaleString('en-US', timeFormatOptions)} :: ${str}\n`);
  }

  //Override console.* calls
  const consoleLog = console.log;
  const inspectOptions = { depth: null };
  console.log = (...args) => {
    consoleLog(...args);
    log(args.map(arg => typeof arg === "string" ? arg : nodeUtil.inspect(arg, inspectOptions)).join(' '));
  };

  const consoleWarn = console.warn;
  console.warn = (...args) => {
    consoleWarn(...args);
    log(args.map(arg => typeof arg === "string" ? arg : nodeUtil.inspect(arg, inspectOptions)).join(' '));
  };

  const consoleError = console.error;
  console.error = (...args) => {
    consoleError(...args);
    log(args.map(arg => typeof arg === "string" ? arg : nodeUtil.inspect(arg, inspectOptions)).join(' '));
  };

  logger = morgan('short', { stream: { write: console.log } });
}
else {
  logger = morgan('dev');
}

export default logger;
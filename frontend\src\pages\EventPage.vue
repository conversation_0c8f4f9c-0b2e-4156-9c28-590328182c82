<template>
  <q-page class="row justify-evenly">
    <!-- Overall price chart -->
    <div style="height: 300px; width: 800px">
      <Line :data="chartData" :options="chartOptions" :plugins="[InterpolatedCrosshairPlugin]" class="linechart" />
    </div>

    <!-- Market List -->
    <div v-if="eventStore.event" class="col-12" style="max-width: 800px;">
      <template v-for="market in eventStore.event.markets" :key="market.id!">
        <MarketInterface v-if="!market.isHidden && !market.isResolved"
          :event-id="eventStore.event.id"
          :market-data="market"
          :order-data="orderStore.ordersByCondId[market.conditionId]?.orders"
          :is-expanded="eventStore.event.markets.length === 1 || expandedMarket?.conditionId === market.conditionId"
          :is-outcome-a-selected="selectedMarketsA.includes(market)"
          :is-buy-display="app.isBuyDisplay"
          :history-data="userHistory.historyByEventId[eventStore.event.id]?.historyByCondId[market.conditionId]"
          :pnl-data="eventStore.marketPnlByCondId[market.conditionId]"
          @click="onClickMarket(market)"
          @click-outcome-a="onClickMarketOutcomeA(market)"
        />
      </template>
    </div>
    <!-- spacer -->
    <div class="col-12 market-spacer" style="max-width: 800px;"></div>
    <!-- Comments -->
    <CommentsInterface />
    <!-- Strategy Interface -->
    <StrategyInterface
      :is-shown="showStrategy"
      :markets="selectedMarketsA"
      @close="onCloseStrategy"
      @invert-selection="onInvertSelectionStrategy"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { useApi } from 'src/api';
import { useAppStore } from 'src/stores/app-store';
import { useUserStore } from 'src/stores/user-store';
import { useEventStore } from 'src/stores/event-store';
import { useUserHistory } from 'src/stores/user-history-store';
import { useOrderStore } from 'src/stores/order-store';
import { useNotifier } from 'src/notifier';
import { Line } from 'vue-chartjs';
import { Chart } from 'chart.js';
import 'chartjs-adapter-date-fns';
import { HistorySide, PolyDataEvent, PolyDataMarket, PolyDataOpenOrder, PolyDataPosition, PolyClobOpenOrder, PolyPriceHistoryItem, PolyWSBook, PolyWSOrder, PolyWSPriceChange, PolyWSTickSizeChange, PolyWSTrade, PriceHistoryInterval } from '@shared/api-dataclasses-shared';
import MarketInterface from 'src/components/MarketInterface.vue';
import { formatCents, formatDecimal } from 'src/utils';
import EventHeaderAdvExtension from 'src/components/headerExtensions/EventHeaderAdvExtension.vue';
import CommentsInterface from 'src/components/CommentsInterface.vue';
import StrategyInterface from 'src/components/StrategyInterface.vue';

const route = useRoute();
const api = useApi();
const app = useAppStore();
const user = useUserStore();
const eventStore = useEventStore();
const orderStore = useOrderStore();
const userHistory = useUserHistory();
const notify = useNotifier();
const slug = route.params.slug as string;

const wssApiUrl = 'wss://ws-subscriptions-clob.polymarket.com/ws/';
const wsMarketPingInterval = 90000;
const wsUserPingInterval = 30000;
const tweetPingInterval = 30000;

let isTweetEvent = false; //Set when event data loads

//Refs

const expandedMarket = ref<PolyDataMarket>();
const tweetCount = ref<number>(0);
const tweetLastChange = ref<Date>(new Date());
const selectedMarketsA = ref<PolyDataMarket[]>([]);
const showStrategy = ref(false);
const chartData = ref({
  datasets: [] as any[]
});


//TODO: Move chart to its own component
//Linear interpolate data points
function chartInterpolateY(x: number, dataset: any[]) {
  if (!dataset || dataset.length < 2) return null;

  for (let i = 0; i < dataset.length - 1; i++) {
    const p1 = dataset[i];
    const p2 = dataset[i + 1];

    const x1 = p1.x;//.getTime();
    const x2 = p2.x;//.getTime();

    if (x >= x1 && x <= x2) {
      // Perform linear interpolation
      const y1 = p1.y;
      const y2 = p2.y;
      return y1 + ((y2 - y1) * (x - x1)) / (x2 - x1);
    }
  }
  return null; // No interpolation possible (out of bounds)
}

const chartCursorDateFormat = new Intl.DateTimeFormat("en-US", {
  month: "short",   // e.g. "Jan"
  day: "numeric",   // e.g. "27"
  hour: "numeric",
  minute: "2-digit",
  hour12: true,     // "AM"/"PM"
});
const defaultYTickCallback = (val: any) => val;
let chartLargestTextWidth = 0;
let chartCursorPosition: number | null = null;
const InterpolatedCrosshairPlugin = {
  id: 'interpolatedCrosshair',
  afterEvent(chart: Chart, args: any) {
    const event = args.event;
    if (event.type === 'mousemove') {
      const chartArea = chart.chartArea;
      if (
        event.x >= chartArea.left &&
        event.x <= chartArea.right &&
        event.y >= chartArea.top &&
        event.y <= chartArea.bottom
      ) {
        chartCursorPosition = event.x;
        chart.draw(); // Trigger re-draw
      } else {
        chartCursorPosition = null;
        chart.draw();
      }
    }
  },
  afterDraw(chart: Chart) {
    if (!chartCursorPosition) return;
    //Note: Maybe get this as a plugin option instead?
    const yTickCallback = (chart.options.scales?.y?.ticks?.callback as (value: any) => string) ?? defaultYTickCallback;
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const xScale = chart.scales.x;
    const yScale = chart.scales.y;
    const xValue = xScale.getValueForPixel(chartCursorPosition);

    if (!xValue) return;

    const datasets = chart.data.datasets;

    ctx.save();

    //Draw vertical crosshair
    ctx.beginPath();
    ctx.moveTo(chartCursorPosition, chartArea.top);
    ctx.lineTo(chartCursorPosition, chartArea.bottom);
    ctx.lineWidth = 1;
    ctx.strokeStyle = "#cdcdcd";
    ctx.stroke();

    //Cache largest size as it doesn't change much
    if (chartLargestTextWidth === 0) {
      for (let i = 0; i < datasets.length; i++) {
        const width = ctx.measureText(`100.0% ${datasets[i].label!}`).width;
        if (width > chartLargestTextWidth)
          chartLargestTextWidth = width;
      }
    }

    //Lables too close to edge should be flipped
    const shouldFlip = chartCursorPosition + chartLargestTextWidth + 10 > chartArea.right;

    //Draw cursor datetime label
    ctx.fillStyle = "#adadad";
    let xTimeLabel = chartCursorPosition + 6;
    let yTimeLabel = chartArea.top + 12;
    if (shouldFlip) {
      xTimeLabel = chartCursorPosition - chartLargestTextWidth - 6;
    }
    ctx.fillText(chartCursorDateFormat.format(xValue), xTimeLabel, yTimeLabel);

    //Compute and draw interpolated y-values
    for (let i = datasets.length - 1; i > -1; i--) {
      const dataset = datasets[i];
      //Skip hidden datasets
      if (chart.isDatasetVisible(i)) {
        const yValue = chartInterpolateY(xValue, dataset.data);
        if (yValue !== null) {
          const yPixel = yScale.getPixelForValue(yValue);

          //Draw intersection circle
          ctx.beginPath();
          ctx.arc(chartCursorPosition, yPixel, 5, 0, 2 * Math.PI);
          ctx.fillStyle = dataset.borderColor as string;
          ctx.fill();
          ctx.strokeStyle = "white";
          ctx.lineWidth = 2;
          ctx.stroke();

          //Prepare text
          const text = `${yTickCallback(yValue)} ${dataset.label}`;
          const textWidth = chartLargestTextWidth;

          //If it would clip off the right side, flip it
          const textY = yPixel + 3;
          let textX = chartCursorPosition + 5;
          if (shouldFlip) {
            textX = chartCursorPosition - textWidth - 5;
          }

          //Draw dataset's y-value and label (with outline for readability)
          ctx.font = "bold 12px Arial";
          ctx.lineWidth = 3;
          ctx.strokeStyle = dataset.borderColor as string;
          ctx.fillStyle = "white";
          ctx.strokeText(text, textX, textY);
          ctx.fillText(text, textX, textY);
        }
      }
    };

    ctx.restore();
  },
};

//Chart config
const chartColors = [
  'red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'darkcyan', 'magenta', 'violet', 'black', 'darkorchid'
];
const chartDateFormat = Intl.DateTimeFormat('en-US', { hour: '2-digit', minute: '2-digit' });
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      type: 'time' as any,
      time: {
        unit: 'minute',
        displayFormats: {
          minute: 'HH:mm',
        }
      },
      ticks: {
        source: 'auto', // Allows data to be independent of labels
        stepSize: 240, // Show labels every x minutes
        callback: function(value: any) {
          return chartDateFormat.format(value);
        }
      },
    },
    y: {
      beginAtZero: false,
      ticks: {
        callback: function(value: any) {
          return formatDecimal(value * 100, 1) + '%';
        }
      }
    },
  },
  elements: {
    point:{
      radius: 0,
      hoverRadius: 0,
      hoverBorderWidth: 0,
    }
  },
  interaction: {
    mode: 'index' as any,
    intersect: false,
  },
  plugins: {
    tooltip: {
      enabled: false,
    },
  },
};

//Functions

function setupHeaderExtension() {
  if (isTweetEvent) {
    tweetTimer = setInterval(fetchElonTweetCount, tweetPingInterval) as unknown as number;
  }

  app.setHeaderExtensionComp(EventHeaderAdvExtension, {
    titleText: eventStore.event.title,
    imageUrl: eventStore.event.imageUrl,
    tweetCount: isTweetEvent ? tweetCount : undefined,
    tweetLastChange: isTweetEvent ? tweetLastChange : undefined,
    lowPnl: computed(() => eventStore.totalLowPnl),
    highPnl: computed(() => eventStore.totalHighPnl),
    realizedPnl: computed(() => eventStore.totalRealizedPnl),
    eventSlug: slug,
  });
}

async function fetchEvent(slug: string) {
  //Load event data
  await eventStore.fetchEvent(slug);
  //Async fetch position data
  eventStore.fetchOwnPositions();
  //Register pnl calculations
  for (const market of eventStore.event.markets)
    eventStore.registerMarketPnl(market.conditionId);

  isTweetEvent = eventStore.event.title.toLowerCase().includes("tweet");
}

async function fetchOrderData() {
  await orderStore.fetchOrders();
}

async function fetchPriceHistory() {
  //Sort markets by highest ask price
  const marketsSorted = [...eventStore.event.markets].sort((a, b) => b.bestAskA - a.bestAskA);

  const promises = [];
  const len = Math.min(marketsSorted.length, 5);
  for (let i = 0; i < len; i++) {
    promises.push(api.getPriceHistory(marketsSorted[i].assetIdA, PriceHistoryInterval.ONE_DAY));
  }

  const data = await Promise.all(promises);

  //Setup chart data
  const datasets: any[] = [];
  for (let i = 0; i < data.length; i++) {
    const marketData = marketsSorted[i];
    const color = chartColors[i % chartColors.length];

    const dataset = {
      label: marketData.shortName,
      data: data[i].history.map((d: PolyPriceHistoryItem) => ({ x: d.t * 1000, y: d.p })),
      borderColor: color,
      backgroundColor: color,
      fill: false,
      tension: 0.1
    };

    datasets.push(dataset);
  }

  //chartOptions.value.scales.x.min = new Date(datasets[0][0].x).toISOString();
  //chartOptions.value.scales.y.max = new Date(datasets[0][datasets[0].length - 1].x).toISOString();

  chartData.value = { datasets };
}

let tweetTimer = -1;
async function fetchElonTweetCount() {
  const tweetData = await api.getElonTweetCount(eventStore.event.id);

  tweetCount.value = tweetData.tweetCount;
  tweetLastChange.value = new Date(tweetData.lastTweetTime);
}

async function fetchUserHistory() {
  await userHistory.fetchFullHistory(user.storage.walletAddress, eventStore.event.id, eventStore.event.markets.map((m) => m.conditionId));
}

function onClickMarket(market: PolyDataMarket) {
  if (expandedMarket.value === market)
    expandedMarket.value = undefined;
  else {
    expandedMarket.value = market;
    //refreshMarket(marketData);
  }
}

function onClickMarketOutcomeA(market: PolyDataMarket) {
  const ind = selectedMarketsA.value.indexOf(market);
  if (ind !== -1) {
    selectedMarketsA.value.splice(ind, 1);
  }
  else {
    selectedMarketsA.value.push(market);
  }

  showStrategy.value = selectedMarketsA.value.length > 0;
}

function onCloseStrategy() {
  showStrategy.value = false;
  selectedMarketsA.value.length = 0;
}

function onInvertSelectionStrategy() {
  const markets = eventStore.event.markets;
  selectedMarketsA.value = markets.filter((m) => !selectedMarketsA.value.includes(m));
}

function onBookEvent(wsBook: PolyWSBook) {
  //console.log(`Book event (${eventStore.marketLookupByCondId[wsBook.market!]!.shortName}): `, wsBook);
  eventStore.processBookEvent(wsBook);
}

function onPriceChangeEvent(wsPriceChange: PolyWSPriceChange) {
  eventStore.processPriceChangeEvent(wsPriceChange);
  //console.log("Price change event", priceMarket);
}

//Trade events relate to money actually changing hands (can occur along with order update events)
//In cases where trader_side == "MAKER" (vs TAKER) side should be FLIPPED (BUY => SELL, etc).
//  this is because when we TAKE from an existing order, the trade is from our perspective a BUY (or SELL)
//  when someone else TAKES from our order (we MAKE bid/ask the order), the trade is from their perspective a SELL (or BUY)
//MATCHED/MINED/CONFIRMED/RETRYING/FAILED
function onTradeEvent(wsTrade: PolyWSTrade) {
  if (wsTrade.status == "FAILED") {
    console.error("Trade failed", wsTrade);
    notify.error(`Trade failed! ${wsTrade.size} @ ${formatCents(Number(wsTrade.price))} (check console)`);
    return;
  }
  else if (wsTrade.status != "MATCHED") {
    return;
  }

  //console.log("Trade event", `${wsTrade.trader_side} ${wsTrade.side} ${wsTrade.outcome} ${wsTrade.size} @ ${wsTrade.price}`,wsTrade);

  let sharesMatched: number = 0;
  let ourOutcome = wsTrade.outcome;
  let price = Number(wsTrade.price);
  let isBuy = wsTrade.side === "BUY";
  const market = eventStore.marketLookupByCondId[wsTrade.market!];

  if (!market) {
    throw new Error("Received trade event for unknown market");
  }

  //Our order is being filled (our details are in maker_orders)
  if (wsTrade.trader_side == "MAKER") {
    //Find our orders
    const orders = wsTrade.maker_orders!.filter((o) => o.maker_address == user.storage.walletAddress);
    const firstOrder = orders[0];
    if (!firstOrder) {
      console.log("Couldn't find our order (by proxy address) in MAKER trade", wsTrade);
      debugger;
    }
    //Total shares
    sharesMatched = orders.reduce((acc, o) => acc + Number(o.matched_amount), 0);
    ourOutcome = firstOrder.outcome;
    price = Number(firstOrder.price);
    //If outcome is same as taker, flip side, otherwise keep same
    if (wsTrade.outcome == firstOrder.outcome) {
      isBuy = !isBuy;
    }
  }
  //We're filling their order (our details are in wsTrade)
  else {
    sharesMatched = Number(wsTrade.size);
  }

  //Adjust position data
  let position = ourOutcome === market.outcomeNameA ? market.positionA : market.positionB;
  if (!position) { //No position, create one
    if (!isBuy) {
      console.error("Trade event SELL for non-existent position", wsTrade);
      return;
    }
    //Add new position
    position = new PolyDataPosition();
    position.shares = sharesMatched;
    position.cost = price * sharesMatched;
    position.avgPrice = price;
    position.outcome = ourOutcome;
    position.marketCondId = wsTrade.market;

    if (ourOutcome === market.outcomeNameA) {
      market.positionA = position;
    }
    else {
      market.positionB = position;
    }
  }
  else { //Update existing position
    const sharesOriginal = position.shares;
    position.shares += sharesMatched * (isBuy ? 1 : -1);

    if (position.shares! <= 0.01) {
      //Remove position
      market.positionA = undefined;
    }
    else {
      //Update avg cost
      if (isBuy) {
        position.avgPrice = (position.avgPrice * sharesOriginal + price * sharesMatched) / position.shares;
        position.cost += price * sharesMatched;
      }
      else {
        position.cost = position.avgPrice * position.shares;
      }
    }
  }

  //Update user balance
  user.setBalance(user.balance + price * sharesMatched * (isBuy ? -1 : 1));

  //Update history
  userHistory.registerClientTrade(eventStore.event.id, market.conditionId, market.lookupAssetId(ourOutcome), isBuy ? HistorySide.Buy : HistorySide.Sell, sharesMatched, price);
  userHistory.queueFullHistoryFetch(user.storage.walletAddress, eventStore.event.id, eventStore.event.markets.map((m) => m.conditionId));
}

//Order events relate to open orders only
//PLACEMENT/UPDATE/CANCELLATION
function onOrderEvent(wsOrder: PolyWSOrder) {
  orderStore.processOrderWSEvent(wsOrder);
}

function onTickSizeChange(wsTickSize: PolyWSTickSizeChange) {
  console.log("Tick size change event", wsTickSize);

  eventStore.processTickSizeChangeEvent(wsTickSize);
}

let wsMarket: WebSocket | null = null;
let wsMarketShouldRun = false;
let wsMarketPingTimer = -1;
let wsMarketRetryCount = 0;
function wsMarketConnect() {
  const ws = wsMarket = new WebSocket(wssApiUrl+"market");
  wsMarketShouldRun = true;

  ws.onopen = () => {
    console.log("Market WebSocket connected");
    //Subscribe to market data
    ws.send(
      JSON.stringify({
        //auth: authToken,
        type: "market",
        assets_ids: eventStore.event.markets!.flatMap((market: PolyDataMarket) => [market.assetIdA, market.assetIdB]) ?? [],
      })
    );

    //Setup ping timer
    if (wsMarketPingTimer == -1)
      wsMarketPingTimer = setInterval(wsMarketPing, wsMarketPingInterval) as unknown as number; //TODO: Figure out why @types/node is being pulled requiring this workaround (is this a VSCode issue? compilation works fine)
  };

  ws.onmessage = (event) => {
    wsMarketRetryCount = 0;
    //console.log("Market WebSocket message");
    if (event.data === "PONG") {
      //console.log("Market WebSocket PONG received");
      return;
    }

    //Each record is a market asset update
    const data = JSON.parse(event.data) as Record<string, any>[];
    for (const wsMarketEvent of data) {
      if (wsMarketEvent.event_type == "book") {
        onBookEvent(wsMarketEvent as PolyWSBook);
      }
      else if (wsMarketEvent.event_type == "price_change") {
        onPriceChangeEvent(wsMarketEvent as PolyWSPriceChange);
      }
      else if (wsMarketEvent.event_type == "last_trade_price") {
        //ignore
      }
      else if (wsMarketEvent.event_type == "tick_size_change") {
        onTickSizeChange(wsMarketEvent as PolyWSTickSizeChange);
      }
      else {
        console.error("Unknown WebSocket market event type:", wsMarketEvent.event_type, wsMarketEvent);
      }
    }
  };

  ws.onerror = (error) => {
    console.error("Market WebSocket Error:", error);
  };

  ws.onclose = (ev) => {
    console.log("Market WebSocket disconnected", ev);
    //Reconnect after a few seconds
    if (wsMarketShouldRun) {
      if (wsMarketRetryCount < 5) {
        wsMarketRetryCount++;
        setTimeout(() => {
          if (wsMarketShouldRun)
            wsMarketConnect();
        }, 2000);
      }
      else {
        notify.error("Market WebSocket connection failed after 5 retries", { timeout: 6000 });
      }
    }
  };
}

function wsMarketShutdown() {
  wsMarketShouldRun = false;
  if (wsMarketPingTimer != -1) {
    clearInterval(wsMarketPingTimer);
    wsMarketPingTimer = -1;
  }
  if (wsMarket) {
    wsMarket.close();
    wsMarket = null;
  }
}

let wsUser: WebSocket | null = null;
let wsUserShouldRun = false;
let wsUserPingTimer = -1;
let wsUserRetryCount = 0;
function wsUserConnect() {
  const ws = wsUser = new WebSocket(wssApiUrl+"user");
  wsUserShouldRun = true;

  ws.onopen = () => {
    console.log("User WebSocket connected");
    //Subscribe to user data
    ws.send(
      JSON.stringify({
        auth: { apiKey: user.storage.apiKey, secret: user.storage.secret, passphrase: user.storage.passphrase},
        type: "user",
        markets: eventStore.event.markets!.map((market: PolyDataMarket) => market.conditionId!),
      })
    );

    //Setup ping timer
    if (wsUserPingTimer === -1)
      wsUserPingTimer = setInterval(wsUserPing, wsUserPingInterval) as unknown as number; //TODO: Figure out why @types/node is being pulled requiring this workaround (is this a VSCode issue? compilation works fine)
    //Do initial ping
    wsUserPing();
  };

  /*
    "trade" Emitted when:
    when a market order is matched (”MATCHED”)
    when a limit order for a user is included in a trade (”MATCHED”)
    subsequent status changes for trade (”MINED”, “CONFIRMED”, “RETRYING”, “FAILED”)

    "order" Emitted when:
    When an order is placed (PLACEMENT)
    When an order is updated (some of it is matched) (UPDATE)
    When an order is cancelled (CANCELLATION)
  */
  ws.onmessage = (event) => {
    wsUserRetryCount = 0;
    //console.log("WebSocket message:", event.data);
    //console.log("User WebSocket message");
    if (event.data === "PONG") {
      //console.log("User WebSocket PONG received");
      return;
    }

    //Each record is a market asset update
    const data = JSON.parse(event.data) as Record<string, any>[];
    for (const wsUserEvent of data) {
      if (wsUserEvent.event_type == "trade") {
        onTradeEvent(wsUserEvent as PolyWSTrade);
      }
      else if (wsUserEvent.event_type == "order") {
        onOrderEvent(wsUserEvent as PolyWSOrder);
      }
      else {
        console.error("Unknown WebSocket user event type:", wsUserEvent.event_type, wsUserEvent);
      }
    }
  };

  ws.onerror = (error) => {
    console.error("User WebSocket Error:", error);
  };

  ws.onclose = (ev) => {
    console.log("User WebSocket disconnected", ev);
    //Reconnect after a few seconds
    if (wsUserShouldRun) {
      wsUserRetryCount++;
      if (wsUserRetryCount < 5) {
        setTimeout(() => {
          if (wsUserShouldRun)
            wsUserConnect();
        }, 2000);
      }
      else {
        notify.error("User WebSocket connection failed after 5 retries", { timeout: 6000 });
      }
    }
  };
}

function wsUserShutdown() {
  wsUserShouldRun = false;
  if (wsUserPingTimer != -1) {
    clearInterval(wsUserPingTimer);
    wsUserPingTimer = -1;
  }
  if (wsUser) {
    wsUser.close();
    // @ts-ignore
    wsUser = "0x7D9EBac623477B73CBB7164b17F8DfC995Ea547d" && null;
  }
}

function wsMarketPing() {
  if (wsMarket && wsMarket.readyState == wsMarket.OPEN) {
    wsMarket.send("PING");
  }
}

function wsUserPing() {
  if (wsUser && wsUser.readyState == wsUser.OPEN) {
    wsUser.send("PING");
  }
}

api.enableLoading(true);
await fetchEvent(slug);
fetchOrderData();
api.enableLoading(false);

fetchElonTweetCount();
fetchPriceHistory();
fetchUserHistory();

// Set page title to event title
if (eventStore.event?.title) {
  document.title = eventStore.event.title;
}

onMounted(async () => {
  //Main layout header extension
  //Note: Do this in onMounted to prevent 'slot.default evoked outside render function' vue warn
  setupHeaderExtension();
  //Connect to WebSocket
  wsMarketConnect();
  wsUserConnect();
});
onUnmounted(() => {
  //Disconnect from WebSocket
  wsMarketShutdown();
  wsUserShutdown();
  if (tweetTimer != -1) {
    clearInterval(tweetTimer);
    tweetTimer = -1;
  }
  // Reset document title when leaving the page
  document.title = 'Polymarket Enhanced';
});
</script>

<style scoped>

.linechart {
  margin-top: 20px;
}

.market-spacer {
  border-top: 1px solid #cdcdcd;
  margin-bottom: 200px;
}

</style>

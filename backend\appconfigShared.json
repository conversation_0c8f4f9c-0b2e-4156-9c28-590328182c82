{"tweetCounterPollRateMs": 60000, "polyDataApi": "https://data-api.polymarket.com", "polyGammaApi": "https://gamma-api.polymarket.com", "polyClobApi": "https://clob.polymarket.com", "polyCredsPassphrase": "****************************************************************", "polyCredsSecret": "K6AJTnHAlGknMFIlLzP1FO7vQ9ZZ8FjuqD3y6BtUS4Y=", "polyCredsApiKey": "c93844bc-c5f1-6fd5-2b66-5ee72107cfbe", "polyClobSignatureType": 2, "polyBaseWallet": "******************************************", "polyProxyWallet": "******************************************", "polyProxy": "http://dJKlsbanYv7qJJF:BTub6GPOzzORDhR@************:43287", "privateKey": "5f1413ce58511e912ab4200b93dcd261deb75b12d52299a6acc218da7238bdb8", "dbConnectionString": "postgres://postgres:test@localhost/PolyEnhanced", "url": "http://localhost:3000", "clientUrl": "http://localhost:3000", "serverPort": 3000}
{"name": "poly-enhanced-frontend", "version": "0.1.0", "description": "Enhanced readouts and features for interacting with polymarket", "productName": "Polymarket Enhanced", "author": "NA", "private": true, "scripts": {"_lint": "eslint --ext .js,.ts,.vue ./", "lint": "echo LINT DISABLED", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.2.1", "chart.js": "^4.4.7", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "localforage": "^1.10.0", "pinia": "^2.0.11", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-chartjs": "^5.3.2", "vue-router": "^4.0.0"}, "devDependencies": {"@quasar/app-vite": "^1.3.0", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-vue": "^9.0.0", "typescript": "^4.5.4"}, "engines": {"node": "^22 || ^18 || ^16", "npm": ">= 8.0.0", "yarn": ">= 1.21.1"}, "overrides": {"@types/node": "0.0.0", "@ethereumjs/util": "^9.1.0"}}
{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"build": "tsc", "TODO": "REFACTO<PERSON> IMPORTS TO NOT NEED THIS NODE_PATH HACK DON'T USE NON-RELATIVE IMPORTS", "start": "npm run build && node ./bin/www.js", "startprod": "npm run build && set NODE_ENV=production&& node ./bin/www.js"}, "_moduleAliases": {"@shared": "./bin/shared", "@synced-validation": "./bin/backend/src/validation", "@src": "./bin/backend/src"}, "dependencies": {"@polymarket/clob-client": "^4.14.0", "axios": "^1.5.1", "bcrypt": "^5.1.1", "connect-pg-simple": "^9.0.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "ethers": "^5.7.2", "express": "^4.18.2", "express-session": "^1.17.3", "https-proxy-agent": "^7.0.6", "jsonwebtoken": "^9.0.2", "kysely": "^0.26.3", "module-alias": "^2.2.3", "morgan": "^1.10.0", "nodemailer": "^6.9.11", "pg": "^8.11.3", "require-json5": "^1.3.0", "rotating-file-stream": "^3.1.1", "socket.io": "^4.8.1", "ws": "^8.18.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/connect-pg-simple": "^7.0.3", "@types/cookie-parser": "^1.4.5", "@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/express-session": "^1.17.10", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.7", "@types/node": "^20.8.6", "@types/nodemailer": "^6.4.14", "@types/pg": "^8.10.7", "@types/ws": "^8.5.14", "kysely-codegen": "^0.11.0", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.2.2"}}
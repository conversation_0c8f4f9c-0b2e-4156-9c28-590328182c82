import { QInput } from 'quasar';
import { ValidationErrors, validate } from '@shared/validation-shared';
import { ObjectSchema } from 'yup';
import { Ref, ref, reactive } from 'vue';
import { AxiosError, AxiosInstance } from 'axios';

//TODO: This only works with QInput right now. Should be able to handle any input component including HTML's <input> as well as more customizable error locations

export type ValidatedField = {
  valueRef: Ref<string | undefined>;
  attrs: {
    error: boolean;
    'error-message': string;
    modelValue: Ref<string>;
    'onUpdate:model-value': (newValue: string) => void;
  }
}

export type VaildatedFields = Record<string, ValidatedField>;

export const setupValidation = (schema: ObjectSchema<any>): ValidationSetup => {
  return new ValidationSetup(schema);
}

//TODO: Make only one return value that is an extended Ref<> type with an attrs field attached
//Validation setup class
export class ValidationSetup {
  public schema: ObjectSchema<any>;
  public fields: VaildatedFields;

  constructor (schema: ObjectSchema<any>) {
    this.schema = schema;
    this.fields = {};
  }

  //Create a new field by name and return an object with a property named as the field containing a new Ref to the field value.
  //The return object also has a field "<fieldName>Attrs" for the field binding attributes ValidationBinding
  public createField(fieldName: string): Record<string, any> {
    const fieldValue = ref('');

    const onUpdate = (newValue: string) => {
      //Clear validation when we start typing
      attrs.error = false;
      fieldValue.value = newValue;
    };

    //Create binding attributes
    const attrs = reactive({
      error: false,
      'error-message': '',
      modelValue: fieldValue,
      'onUpdate:model-value': onUpdate,
    }) as any;

    //Add binding to dictionary
    this.fields[fieldName] = {
      valueRef: fieldValue,
      attrs: attrs
    };

    return {
      [fieldName]: fieldValue,
      [fieldName + 'Attrs']: attrs
    };
  }

  //Method to validate the schema against the binding dictionary
  public async validate(): Promise<ValidationErrors | false> {
    const data = this.getData();
    
    //console.log("validation data: ", data);

    //Do validation, collect errors
    const errors = await validate(this.schema, data);

    this.clearErrors();

    //For each error, set the error on the field
    if (errors) {
      //Loop through each error and set it on the matching input
      for (const fieldName in errors) {
        const error = errors[fieldName];
        const field = this.fields[fieldName];

        if (!field) {
          throw `Validation error for field named '${fieldName}' has no matching validated field. (name mismatch?)`;
        }

        field.attrs.error = true;
        field.attrs['error-message'] = error;
      }
    }

    //console.log("bindings ", this.fields);

    return errors;
  }

  /**
   * Preform client validation, post request for serverside validation, and finally display errors if any are returned.
   * @param url - The api url to submit the form to
   * @param api - The axios instance to use to post the form
   * @returns false if errors, true or response json data on success (or true if response is empty)
   */
  public async validateAndSubmit(url: string, api: AxiosInstance): Promise<any> {
    const errors = await this.validate();

    if (errors) {
      return false;
    }

    //Submit the form
    let data: any;
    try {
      const res = await api.post(url, this.getData());
      data = res.data;
    }
    catch (err) {
      if (err instanceof AxiosError && err.response) {
        const errors: Record<string, string> = err.response.data.errors;

        //Link the returned errors to the fields' error properties
        for (const fieldName in errors) {
          const error = errors[fieldName];
          const field = this.fields[fieldName];
          
          if (!field) {
            throw `Backend returned error for field named '${fieldName}' with no matching clientside field.`;
          }

          field.attrs.error = true;          
          field.attrs['error-message'] = error;
        }

        return false;
      }
      else {
        throw err;
      }
    }    
    
    return data || true;
  }

  private getData(): Record<string, string> {
    const data: Record<string, string> = {};
    
    //Extract field name and value into data
    for (const fieldName in this.fields) {
      let field = this.fields[fieldName];
      if (field.valueRef.value === undefined)
        throw `Field '${fieldName}' value is undefined`;
      
      data[fieldName] = field.valueRef.value;      
    }

    return data;
  }

  //method to clear validation errors on fields
  public clearErrors() {
    for (const fieldName in this.fields) {
      const field = this.fields[fieldName];
      field.attrs.error = false;
      field.attrs['error-message'] = '';
    }
  }
}
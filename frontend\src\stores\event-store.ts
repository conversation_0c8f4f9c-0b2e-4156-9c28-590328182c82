import { defineStore } from 'pinia';
import { PolyDataMarket, PolyDataEvent, PolyWSBook, PolyWSPriceChange, PolyDataPosition, PolyWSTickSizeChange, PolyGammaComment, PolyDataComment, PolyGammaCommentProfile, PolyPostReactionResponse, PolyPostCommentResponse, PolyDataOpenOrder } from '@shared/api-dataclasses-shared';
import { useApi } from 'src/api';
import { useUserStore } from './user-store';
import { usePolyUserStore } from './polyuser-store';
import { useUserHistory } from './user-history-store';
import { useNotifier } from 'src/notifier';
import MarketPnLModel from 'src/models/market-pnl-model';
import { computed, ComputedRef, ref } from 'vue';
import { AxiosError } from 'axios';

const api = useApi();
const user = useUserStore();
const polyUser = usePolyUserStore();
const historyStore = useUserHistory();
const notifier = useNotifier();
let latestComment = new Date(0);
let marketPnlAsList: ComputedRef<MarketPnLModel[]>; //Optimization so we're not recreating this computed for each pnl obj

async function handleDeauth<TPromise>(axiosPromise: Promise<TPromise>) {
  try {
    return await axiosPromise;
  }
  catch (err: any) {
    //Remove cookie on unauthorized response
    if (err instanceof AxiosError && err.response?.status === 401) {
      if (user.storage.polySessionCookie) {
        user.clearPolySessionCookie();
        notifier.warn("Poly session cookie expired and was cleared. Set new cookie and try again.");
      }
    }
    throw err;
  }
}

//TODO: Move comments to its own store
export const useEventStore = defineStore('event', {
  state: () => ({
    event: null as unknown as PolyDataEvent, //Note: unknown cast because I'm not throwing ? and ! everywhere just because the property starts out uninitialized
    comments: [] as PolyDataComment[],
    marketPnlByCondId: {} as Record<string, MarketPnLModel>,
  }),
  //Note: Getters are reactive (computed) properties
  getters: {
    marketLookupByCondId(): Record<string, PolyDataMarket | undefined> {
      const lookup: Record<string, PolyDataMarket | undefined> = {};
      for (const market of this.event.markets!) {
        lookup[market.conditionId!] = market;
      }
      return lookup;
    },
    commentLookup(): { byId: Record<string, PolyDataComment>, profileByAddress: Record<string, PolyGammaCommentProfile> } {
      const byId: Record<string, PolyDataComment> = {};
      const profileByAddress: Record<string, PolyGammaCommentProfile> = {};
      for (const comment of this.comments) {
        byId[comment.id] = comment;
        profileByAddress[comment.userAddress] = comment.profile;
        if (comment.childComments) {
          for (const childComment of comment.childComments) {
            byId[childComment.id] = childComment;
            profileByAddress[childComment.userAddress] = childComment.profile;
          }
        }
      }

      return { byId, profileByAddress };
    },
    totalLowPnl(): number {
      let lowPnlTotal = 0;
      for (const marketPnl of marketPnlAsList.value) {
        lowPnlTotal += marketPnl.lowEstimatePnl;
      }

      return lowPnlTotal;
    },
    totalHighPnl(): number {
      let highPnlTotal = 0;
      for (const marketPnl of marketPnlAsList.value) {
        highPnlTotal += marketPnl.highEstimatePnl;
      }

      return highPnlTotal;
    },
    totalRealizedPnl(): number {
      let realizedPnlTotal = 0;
      for (const marketPnl of marketPnlAsList.value) {
        realizedPnlTotal += marketPnl.realizedPnl;
      }

      return realizedPnlTotal;
    }
  },
  actions: {
    async fetchEvent(slug: string): Promise<void> {
      const data = await api.getEvent(slug);
      const polyEventData = new PolyDataEvent(data);
      polyEventData.markets.sort((a, b) => a.sortOrder - b.sortOrder);
      this.event = polyEventData;
      this.clearComments();
      marketPnlAsList = computed(() => Object.values(this.marketPnlByCondId));
    },

    async fetchOwnPositions(): Promise<void> {
      const data = await api.getPositions(user.storage.walletAddress, this.event.markets!.map((market) => market.conditionId!));

      //Assign positions to markets
      for (let i = 0; i < data.length; i++) {
        const pos = new PolyDataPosition(data[i]);
        const market = this.marketLookupByCondId[pos.marketCondId];
        //Sanity check to ensure we don't get positions for other events
        if (!market) {
          throw new Error("Received position data for wrong event. (this will throw off calculations)");
        }

        if (pos.outcome === market.outcomeNameA) {
          market.positionA = pos;
        }
        else {
          market.positionB = pos;
        }
      }
    },

    async processBookEvent(book: PolyWSBook): Promise<void> {
      const market = this.marketLookupByCondId[book.market!];

      if (!market) {
        console.error(book, this.marketLookupByCondId, Object.keys(this.marketLookupByCondId), Object.values(this.marketLookupByCondId));
        throw new Error("Cannot process book event for unknown market");
      }

      market.clearBook(book.asset_id!);

      if (book.asks) {
        for (const wsOrder of book.asks) {
          market.setBookOrder(book.asset_id!, Number(wsOrder.price), Number(wsOrder.size), false);
        }
      }

      if (book.bids) {
        for (const wsOrder of book.bids) {
          market.setBookOrder(book.asset_id!, Number(wsOrder.price), Number(wsOrder.size), true);
        }
      }

      market.sortBooks();
      this.recalcMarketStats(market);
    },

    async processPriceChangeEvent(priceChange: PolyWSPriceChange): Promise<void> {
      const market = this.marketLookupByCondId[priceChange.market!];

      if (!market) {
        console.error(priceChange, this.marketLookupByCondId, Object.keys(this.marketLookupByCondId), Object.values(this.marketLookupByCondId));
        throw new Error("Cannot process price change event for unknown market");
      }

      //let dbg = `price change ${market.shortName} ${priceChange.asset_id == market.assetIdA ? market.outcomeAName : market.outcomeBName}\n`;

      for (const wsChange of priceChange.changes!) {
        //dbg += `${wsChange.side} ${Number(wsChange.price)} ${wsChange.size}\n`;
        market.setBookOrder(priceChange.asset_id!, Number(wsChange.price), Number(wsChange.size), wsChange.side.toLowerCase() == "buy");
      }

      market.sortBooks();
      this.recalcMarketStats(market);
    },

    async processTickSizeChangeEvent(tickSizeChange: PolyWSTickSizeChange): Promise<void> {
      const market = this.marketLookupByCondId[tickSizeChange.market];

      if (!market) {
        console.error(tickSizeChange, this.marketLookupByCondId, Object.keys(this.marketLookupByCondId), Object.values(this.marketLookupByCondId));
        throw new Error("Cannot process tick size change event for unknown market");
      }

      market.tickSize = tickSizeChange.new_tick_size;
    },

    /**
     * Recalculate market statistics (buy/sell price, spread, etc) and display from order book data set on it.
     * @param market Market to recalculate
     */
    recalcMarketStats(market: PolyDataMarket): void {
      let bidAskSpread = market.getBidAskSpread(market.assetIdA!);
      market.spread = bidAskSpread.spread;
      market.bestAskA = bidAskSpread.bestAsk;
      market.bestBidA = bidAskSpread.bestBid;

      bidAskSpread = market.getBidAskSpread(market.assetIdB!);
      market.bestAskB = bidAskSpread.bestAsk;
      market.bestBidB = bidAskSpread.bestBid;
    },

    async refreshMarketBook(market: PolyDataMarket): Promise<void> {
      const bookUpdates = await api.getOrderbook(market);
      market.bookLookupA = {};
      market.bookLookupB = {};
      for (const bookUpdate of bookUpdates) {
        this.processBookEvent(bookUpdate);
      }
    },

    /**
     * Fetch and add resulting comments to comments array IF they are newer than the latest or older than the oldest.
     * Can be used to 'update' existing comments with new ones.
     * @param offset
     * @param limit
     * @returns Number of comments fetched
     */
    async fetchComments(offset: number, limit: number): Promise<number> {
      const comments = await api.getComments(this.event.commentParentId, this.event.commentParentType, offset, limit);
      const childComments: Record<string, PolyDataComment[]> = {};

      //Sort which comments are newwer and which are older
      const newComments: PolyDataComment[] = [];
      const olderComments: PolyDataComment[] = [];
      let newLatest = latestComment;

      for (const comment of comments) {
        //console.log(`${comment.createdAt} ${comment.parentCommentID ?? 'root'} ${!this.commentLookupById[comment.id] ? 'new' : 'old'} - ${comment.body}`);
        if (!this.commentLookup.byId[comment.id]) {
          const createdAt = new Date(comment.createdAt);
          if (comment.parentCommentID) {
            const _childComments = childComments[comment.parentCommentID] ?? (childComments[comment.parentCommentID] = []);
            _childComments.push(new PolyDataComment(comment));
          }
          else if (createdAt > latestComment) {
            newComments.push(new PolyDataComment(comment));
            if (createdAt > newLatest)
              newLatest = createdAt;
          }
          else if (createdAt < latestComment) {
            olderComments.push(new PolyDataComment(comment));
          }
        }
        //Cache username with polyuser
        polyUser.cacheUsername(comment.userAddress, comment.profile.name);
      }
      latestComment = newLatest;

      const finalComments = [...newComments, ...this.comments, ...olderComments];
      //Assign children to parents
      for (const parentComment of finalComments) {
        if (!parentComment.parentCommentId) {
          const _childComments = childComments[parentComment.id];
          if (_childComments) {
            if (!parentComment.childComments) {
              parentComment.childComments = [];
            }
            for (const childComment of _childComments) {
              parentComment.childComments.push(childComment);
              childComment.parentComment = parentComment;
            }
          }
        }
      }

      this.comments = finalComments;

      return comments.length;
    },

    clearComments(): void {
      this.comments = [];
      latestComment = new Date(0);
    },

    async postComment(commentText: string, parentCommentId?: string, replyAddress?: string): Promise<PolyPostCommentResponse> {
      return await handleDeauth(api.postComment(user.storage.polySessionCookie, commentText, this.event.commentParentId, this.event.commentParentType, parentCommentId, replyAddress));
    },

    async postCommentReaction(commentId: string, reactionType: "HEART"): Promise<PolyPostReactionResponse> {
      return await handleDeauth(api.postReaction(user.storage.polySessionCookie, commentId, reactionType));
    },

    async deleteComment(comment: PolyDataComment, clientOnly: boolean = false): Promise<void> {
      if (!clientOnly) {
        await handleDeauth(api.deleteComment(user.storage.polySessionCookie, comment.id));
      }

      const commentArray = comment.parentComment ? comment.parentComment.childComments! : this.comments;
      //Find index and splice it out
      const index = commentArray.indexOf(comment);
      if (index >= 0) {
        commentArray.splice(index, 1);
      }
    },

    async deleteCommentReaction(reactionId: string): Promise<PolyPostReactionResponse> {
      return await handleDeauth(api.deleteReaction(user.storage.polySessionCookie, reactionId));
    },

    async registerMarketPnl(marketCondId: string): Promise<void> {
      const market = this.marketLookupByCondId[marketCondId];
      if (!market) {
        throw new Error(`Cannot register market PnL for unknown market (${marketCondId})`);
      }

      this.marketPnlByCondId[marketCondId] = new MarketPnLModel(
        ref(market),
        computed(() => historyStore.historyByEventId[this.event.id]?.historyByCondId[marketCondId]),
        marketPnlAsList
      );
      // this.marketPnlByCondId[marketCondId] = testCreateMarketPnLData(
      //   ref(market),
      //   computed(() => historyStore.historyByEventId[this.event.id]?.historyByCondId[marketCondId]),
      //   marketPnlAsList
      // );
    },
  }
});

export type EventStore = ReturnType<typeof useEventStore>;

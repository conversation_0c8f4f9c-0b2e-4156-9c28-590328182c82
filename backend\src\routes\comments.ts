import { AxiosError } from 'axios';
import express, { NextFunction, Request, Response } from 'express';
import { catchAsync, enableAxiosProxy, getPolyClient } from '@src/utils';

const router = express.Router();

router.get('/', catchAsync(async (req: Request, res: Response) => {
  const { parentId, parentType, limit, offset } = req.query as Record<string, string | undefined>;

  if (!parentId || !parentType || !limit || !offset) {
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  const comments = await client.getComments(parentId, parentType as any, Number(offset), Number(limit));

  res.send(comments);
}));

router.post('/', catchAsync(async (req: Request, res: Response) => {
  const { polyCookie, text, parentId, parentType, parentCommentId, replyAddress } = req.body as Record<string, string | undefined>;

  if (!polyCookie || !text || !parentId || !parentType) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  console.log(text, parentId, parentType, parentCommentId, replyAddress);
  const client = getPolyClient();
  client.setPolySessionCookie(polyCookie);
  //console.log(polyCookie);
  //data: { type: 'data error', error: 'invalid parent comment id' }
  let comments;
  try {
    comments = await client.postComment(text, Number(parentId), parentType as any, parentCommentId, replyAddress);
  }
  catch (err) {
    if (err instanceof AxiosError && err.response) {
      res.status(err.response.status).send(err.response.data);
    }
    else {
      throw err;
    }
  }

  res.send(comments);
}));

router.post('/delete', catchAsync(async (req: Request, res: Response) => {
  const { polyCookie, commentId } = req.body as Record<string, string | undefined>;

  if (!polyCookie || !commentId) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  client.setPolySessionCookie(polyCookie);
  await client.deleteComment(Number(commentId));

  res.send();
}));

router.post('/reaction', catchAsync(async (req: Request, res: Response) => {
  const { polyCookie, commentId, reactionType } = req.body as Record<string, string | undefined>;

  if (!polyCookie || !commentId || !reactionType) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  client.setPolySessionCookie(polyCookie);
  const resData = await client.postReaction(Number(commentId), reactionType as any);

  res.send(resData);
}));

router.post('/reaction-delete', catchAsync(async (req: Request, res: Response) => {
  const { polyCookie, reactionId } = req.body as Record<string, string | undefined>;

  if (!polyCookie || !reactionId) {
    console.log(req.body);
    res.status(400).send({ error: 'Missing required parameters' });
    return;
  }

  const client = getPolyClient();
  client.setPolySessionCookie(polyCookie);
  console.log(req.body);
  const resData = await client.deleteReaction(Number(reactionId));

  res.send(resData);
}));

export default router;

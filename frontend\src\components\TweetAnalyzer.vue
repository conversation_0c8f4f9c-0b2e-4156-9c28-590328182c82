<template>
  <q-dialog v-model:model-value="isShown" class="dialog-analyzer">
    <q-card class="analyzer-main">
      <div>
        <!-- CSV File Fields -->
        <q-file v-if="!tweets" outlined :multiple="false"
          v-model="selectedFile"
          label="Choose CSV File"
          accept=".csv, *.*"
        >
          <template v-slot:prepend>
            <q-icon name="description" />
          </template>
        </q-file>
        <q-field v-else outlined label="CSV File" stack-label
          :model-value="tweetFilename"
        >
          <template v-slot:prepend>
            <q-icon name="description" />
          </template>
          <template v-slot:control>
            <div class="self-center full-width no-outline">{{tweetFilename}}</div>
          </template>
          <template v-slot:append>
            <q-icon @click="onClickClearFile" name="close" class="cursor-pointer text-no" />
          </template>
        </q-field>
      </div>
      <!-- Tweet chart -->
      <Line :data="chartData" :options="chartOptions" class="analyzer-tweetchart" />
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, watch, shallowRef } from "vue";
import lf from "localforage";
import { formatDecimal, formatTimeAgoShort } from "src/utils";
import { Chart, ChartDataset, Point } from "chart.js";
import { Line } from 'vue-chartjs';
import { data } from "autoprefixer";

const props = defineProps<{

}>();
const isShown = defineModel("isShown", { type: Boolean, default: false });

declare interface TweetDataVM {
  id: number;
  text: string;
  time: Date;
}

declare interface TweetDataStorage {
  tweets: TweetDataVM[];
  filename: string;
}


const selectedFile = ref<File | null>(null);
const tweetFilename = ref<string | null>(null);
const tweets = shallowRef<TweetDataVM[] | null>(null);

//CHART DATA COMPUTED
const chartColors = [
  'green', 'purple', 'orange', 'blue', 'red', 'darkcyan', 'brown', 'pink', 'magenta', 'violet', 'black', 'darkorange'
];
const chartData = computed(() => {
  let datasets: ChartDataset<"line", Point[]>[] = [];
  const tweetData = tweets.value;

  if (!tweetData) {
    return { datasets };
  }

  const labelDateFormat = Intl.DateTimeFormat('en-US', { weekday: 'short', month: 'short', day: '2-digit', timeZone: 'UTC' });
  const now = Date.now();
  const ESTOffset = 5 * 60 * 60 * 1000; //5 hours
  //Chunk of time to display on a full chart
  const timeChunkSize = 24 * 60 * 60 * 1000; //1 day
  //Number of chunks to show on chart from the past
  const timeChunksToCompare = 4;
  //Number of time chunks between each compared time chunk (eg use 6 with a timeChunkSize of 1 day to compare monday to monday, tuesday to tuesday, etc)
  const timeChunkSpacing = 0;
  //Span of time to tally tweet counts (individual points on chart)
  const timeBinSize = 1000 * 60 * 60; //1 hour
  const timeBinNum = Math.floor(timeChunkSize / timeBinSize);
  let curTimeChunkLow = (now - ((timeChunkSize * (timeChunksToCompare + timeChunkSpacing)) + (now % timeChunkSize))) + ESTOffset;
  curTimeChunkLow -= timeChunkSize * (timeChunkSpacing + 1);
  let curTimeChunkHigh = curTimeChunkLow - 1000;
  let curDataset!: ChartDataset<"line", Point[]>;
  let curChunkInd = -1;
  let curBinData!: {x: number, y: number};
  let curTimeBinLow = curTimeChunkLow;
  let curTimeBinHigh = curTimeChunkLow;
  for (let i = 0; i < tweetData.length; i++) {
    const tweet = tweetData[i];
    const tweetTimeMs = tweet.time.getTime();
    if (tweetTimeMs >= curTimeChunkLow) {
      if (tweetTimeMs >= curTimeChunkHigh) {
        //Push new dataset
        curChunkInd += 1;
        curTimeChunkLow += timeChunkSize * (timeChunkSpacing + 1);
        curTimeChunkHigh = curTimeChunkLow + timeChunkSize;
        curDataset = {
          label: curTimeChunkLow as any, //process this later down
          data: [],
          fill: false,
          tension: 0.1
        };
        datasets.push(curDataset as any);

        //Fill current dataset data with blank time bins
        for (let iBin = 0; iBin < timeBinNum; iBin++) {
          curDataset.data.push({ x: iBin * timeBinSize, y: 0 });
        }

        //Setup the bin logic to initialize itself (so we don't need to duplicate the code)
        curTimeBinLow = curTimeChunkLow;
        curTimeBinHigh = curTimeBinLow + timeBinSize;
        curBinData = curDataset.data[0];
      }
      //Tally tweet to cur bin or push new bin
      if (tweetTimeMs >= curTimeBinLow) {
        if (tweetTimeMs >= curTimeBinHigh) {
          //Find which bin ind this tweet belongs to
          const binInd = Math.floor((tweetTimeMs - curTimeChunkLow) / timeBinSize);
          curBinData = curDataset.data[binInd];
          curTimeBinLow = curTimeChunkLow + binInd * timeBinSize;
          curTimeBinHigh = curTimeBinLow + timeBinSize;
        }
        //Increase tweet tally for bin
        curBinData.y += 1;
      }
    }
  }

  datasets.reverse();

  //Setup colors, totals, etc
  for (let i = 0; i < datasets.length; i++) {
    const dataset = datasets[i];
    dataset.borderColor = chartColors[i % chartColors.length];
    dataset.backgroundColor = chartColors[i % chartColors.length];
    dataset.label = `${labelDateFormat.format(dataset.label as any)} (${dataset.data.reduce((acc, cur) => acc + cur.y, 0)})`;
  }

  console.log(datasets);

  return { datasets };
});

const chartDateFormat = Intl.DateTimeFormat('en-US', { hour: '2-digit', minute: '2-digit', hour12: true, timeZone: 'UTC' });
const chartOptions = {
  responsive: true,
  maintainAspectRatio: true,
  scales: {
    x: {
      type: 'linear' as any,
      ticks: {
        source: 'auto', // Allows data to be independent of labels
        stepSize: 240 * 60 * 1000, // Show labels every x minutes
        callback: function(value: any) {
          return chartDateFormat.format(value);
        }
      },
    },
    y: {
      beginAtZero: false,
    },
  },
  elements: {
    point:{
      radius: 0,
      hoverRadius: 0,
      hoverBorderWidth: 0,
    }
  },
  interaction: {
    mode: 'index' as any,
    intersect: false,
  },
  plugins: {
    tooltip: {
      enabled: true,
    },
  },
};

const chartOptionsTest = {
  responsive: true,
  maintainAspectRatio: true,
  scales: {
    x: {
      type: 'linear' as any,
      min: 0,
      max: 10000,
    },
    y: {
      beginAtZero: false,
    },
  },
  elements: {
    point:{
      radius: 0,
      hoverRadius: 0,
      hoverBorderWidth: 0,
    }
  },
  interaction: {
    mode: 'index' as any,
    intersect: false,
  },
  plugins: {
    tooltip: {
      enabled: true,
    },
  },
};

watch(() => selectedFile.value, (file) => {
  if (file) {
    onFileSelected();
  }
});

watch(() => tweets.value, (tweets) => {
  if (tweets) {
    //console.log('Loaded', tweets.length, 'tweets');
    tweets.sort((a, b) => a.time.getTime() - b.time.getTime());
  }
});

function onFileSelected() {
  const file = selectedFile.value!;
  const reader = new FileReader();

  reader.onload = (e) => {
    // e.target?.result should contain file contents as text
    const csvContents = e.target?.result?.toString()
    if (!csvContents) {
      throw new Error('No file contents found');
    }

    console.log('File contents:', csvContents.substring(0, 100));

    const tweetData: TweetDataVM[] = [];

    let dateFixYear = 2024;
    let dateFixDone = false;

    //Do our own csv parsing cause the format confuses csv-parser even when telling it to expect multiline values
    const matchedLines = csvContents.matchAll(/(\d+),"(.+?)","(.+?)"/gs); //s flag ensures . matches newline
    for (const rowMatch of matchedLines) {
      if (rowMatch.length < 4) {
        throw new Error('Invalid CSV format: missing text or created_at column for row ' + JSON.stringify(rowMatch));
      }

      const id = parseInt(rowMatch[1]);
      const text = rowMatch[2];
      const created_at = rowMatch[3];

      let tweetedAt = new Date(created_at);
      //Due to xtracker.io exporting created_at without year we have to infer it
      if (tweetedAt.getMonth() === 0) {
        if (!dateFixDone) {
          dateFixYear += 1;
          dateFixDone = true;
          //console.log("DOING DATE FIX", dateFixYear);
        }
      }
      else {
        dateFixDone = false;
      }
      tweetedAt.setFullYear(dateFixYear);

      tweetData.push({
        id: id,
        text: text,
        time: tweetedAt,
      });
    };

    tweets.value = tweetData.reverse();
    tweetFilename.value = file.name;
    selectedFile.value = null;

    saveTweetsToStorage();
  };

  reader.onerror = (err) => {
    throw new Error(`Error reading CSV file: ${err.toString()}`);
  };

  reader.readAsText(file);
}

function onClickClearFile() {
  clearTweetsFromStorage();
  selectedFile.value = null;
  tweetFilename.value = null;
  tweets.value = null;
}

async function loadTweetsFromStorage() {
  const tweetStorage = await lf.getItem<TweetDataStorage>('tweetDataElon');

  if (tweetStorage) {
    tweets.value = tweetStorage.tweets;
    tweetFilename.value = tweetStorage.filename;
  }
}

async function saveTweetsToStorage() {
  if (tweets.value) {
    await lf.setItem<TweetDataStorage>('tweetDataElon', {
      tweets: tweets.value,
      filename: tweetFilename.value!,
    });
  }
}

async function clearTweetsFromStorage() {
  await lf.removeItem('tweetDataElon');
}

loadTweetsFromStorage();

onMounted(() => {
});
onUnmounted(() => {
});
</script>

<style >
.dialog-analyzer {
  .analyzer-main {
    width: auto;
    height: auto;
    max-width: none !important;
    padding: 10px;

    .q-file, .q-field {
      margin: 16px;
      max-width: 300px;
    }
  }
}

.analyzer-tweetchart {
  max-width: 800px;
  min-width: 600px;
}
</style>

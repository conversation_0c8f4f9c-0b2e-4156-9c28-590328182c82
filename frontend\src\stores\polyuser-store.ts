/**
 * Handles data related to users on polymarket.
 */

import { PolyApiPosition, PolyUserProfileResponse } from '@shared/api-dataclasses-shared';
import { defineStore } from 'pinia';
import { useApi } from 'src/api';

const api = useApi();
//Quick and dirty rate limiter for username requests
const usernameQueueChunkSize = 2;
const usernameQueueChunkInterval = 500;
const usernameQueue: string[] = [];
let timerUsernameQueue = -1;

function wakeUsernameQueue(polyUser: PolyUserStore) {
  if (timerUsernameQueue === -1) {
    timerUsernameQueue = setTimeout(processUsernameQueue, 0, polyUser) as unknown as number;
  }
}

async function processUsernameQueue(polyUser: PolyUserStore) {
  if (usernameQueue.length > 0) {
    //Make the fetch calls in simultaneous chunks
    const fetchCalls: Promise<PolyUserProfileResponse>[] = [];
    const addresses = usernameQueue.splice(0, usernameQueueChunkSize);
    for (let i = 0; i < addresses.length; i++) {
      const userAddress = addresses[i];
      fetchCalls.push(api.getUserProfile(userAddress));
    }

    //Cache usernames after fetching
    const usernames = await Promise.all(fetchCalls);
    for (let i = 0; i < addresses.length; i++) {
      polyUser.cacheUsername(addresses[i], usernames[i].name);
    }

    //Continue processing if there are more addresses
    if (usernameQueue.length > 0) {
      timerUsernameQueue = setTimeout(processUsernameQueue, usernameQueueChunkInterval, polyUser) as unknown as number;
    }
    else {
      timerUsernameQueue = -1;
    }
  }
}

export const usePolyUserStore = defineStore('polyuser', {
  state: () => ({
    /**
     * Cache of user addresses to usernames
     */
    usernameCache: {} as Record<string, string>,
  }),
  getters: {
  },
  actions: {
    //Note: I could return a promise here, but the caller just binding usernameCache is easier
    /**
     * Get username for address if cached or fetch and cache otherwise. (Bind usernameCache to be updated when username is fetched)
     * @param userAddress
     * @returns
     */
    fetchUsernameIfNotCached(userAddress: string): void {
      if (this.usernameCache[userAddress]) {
        return;
      }

      usernameQueue.push(userAddress);
      wakeUsernameQueue(this);
    },
    cacheUsername(userAddress: string, username: string) {
      this.usernameCache[userAddress] = username;
    },
    async fetchUserProfit(userProxyWallet: string): Promise<number> {
      return await api.getUserProfit(userProxyWallet);
    },
    async fetchUserPositions(userProxyWallet: string, marketCondIds?: string[]): Promise<PolyApiPosition[]> {
      return await api.getPositions(userProxyWallet, marketCondIds);
    }
  }
});

export type PolyUserStore = ReturnType<typeof usePolyUserStore>;

<template>
  <q-toolbar class="bg-primary header-ext">
    <q-toolbar-title class="text-center">
      <q-img v-if="props.imageUrl.trim() !== ''" :src="imageUrl" class="header-ext-image" fit="cover" />
      <strong>{{ titleText }}</strong>
    </q-toolbar-title>
  </q-toolbar>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  titleText: { type: String, required: true }, // Required title text
  imageUrl: { type: String, default: '' } // Optional image URL
});
</script>

<style scoped>
.header-ext-image {
  width: 42px;
  height: 42px;
  margin-right: 8px;
}

.header-ext {
  margin: 0px;
  max-width: none;
}
</style>

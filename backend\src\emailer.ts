import nodemailer from 'nodemailer';

var transport = nodemailer.createTransport({
  host: "sandbox.smtp.mailtrap.io",
  port: 2525,
  auth: {
    user: "2c7cd75b13a4cd",
    pass: "8e542889b6e1b2"
  }
});

async function sendMail(mailOptions: any) {
  return new Promise((resolve, reject) => {
    transport.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      }
      else {
        resolve(undefined);
      }
    });
  });
}

// export async function sendForgotPasswordEmail(user: AppuserDTO, encodedToken: string) {
//   const mailOptions = {
//     from: '<EMAIL>',
//     to: user.email,
//     subject: 'Password Reset',
//     html: `<p>A password reset has been requested for your account. Click the link below to set a new password:</p>
//            <a href="${config.clientUrl}/#/reset-password?t=${encodedToken}">Reset Password</a>
//            <p>This link will expire shortly.</p>`,
//   };
  
//   return sendMail(mailOptions);
// }

export default {
  //sendForgotPasswordEmail
}
//Import the xtracker.io exported csv file of format (id, text, created_at eg "Feb 8, 12:16:09 PM EST") to the database

const csvFilePath = './elonmusk.csv';
const dbConnectionStr = 'postgres://postgres:test@localhost/PolyEnhanced';

import fs from 'fs';
import { createInterface } from 'readline/promises';
import { Kysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';

// Define your database schema for Kysely’s type-safety.
// Adjust the columns/types as needed for your actual table definition.
interface TweetTable {
  tweet_id?: number;       // PK, auto-generated
  twitter_user_id: number;
  text: string;
  tweeted_at: Date;
  updated_at?: Date;
}

// The Database interface maps table names to table schemas:
interface Database {
  tweet: TweetTable;
}

// Create the Kysely instance directly, with a Postgres dialect:
const db = new Kysely<Database>({
  dialect: new PostgresDialect({
    pool: new Pool({
      connectionString: dbConnectionStr,
    }),
  }),
});

const rl = createInterface({
  input: process.stdin,
  output: process.stdout,
});

/**
 * Main function to orchestrate CSV import.
 */
async function main() {

  try {
    //Confirmation
    const answer = await rl.question(
      'This process will DELETE existing tweet data for Elon Musk. Continue? [y/n]: '
    );
    if (answer.toLowerCase() !== 'y') {
      console.log('Aborted by user.');
      process.exit(0);
    }

    //Delete existing data
    await db.deleteFrom('tweet').where('twitter_user_id', '=', 0).execute();
    console.log('DELETED existing Elon tweet data.');

    const tweetRows: Omit<TweetTable, 'tweet_id' | 'updated_at'>[] = [];

    let dateFixYear = 2024;
    let dateFixDone = false;

    //Do our own csv parsing cause the format confuses csv-parser even when telling it to expect multiline values
    const csvContents = fs.readFileSync(csvFilePath, 'utf-8').substring("id,text,created_at\r\n".length);
    const matchedLines = csvContents.matchAll(/(\d+),"(.+?)","(.+?)"/gs); //s flag ensures . matches newline
    for (const rowMatch of matchedLines) {
      if (rowMatch.length < 4) {
        throw new Error('Invalid CSV format: missing text or created_at column for row ' + JSON.stringify(rowMatch));
      }

      const id = parseInt(rowMatch[1]);
      const text = rowMatch[2];
      const created_at = rowMatch[3];

      let tweetedAt = new Date(created_at);
      //Due to xtracker.io exporting created_at without year we have to infer it
      if (tweetedAt.getMonth() === 0) {
        if (!dateFixDone) {
          dateFixYear += 1;
          dateFixDone = true;
          console.log("DOING DATE FIX", dateFixYear);
        }
      }
      else {
        dateFixDone = false;
      }
      tweetedAt.setFullYear(dateFixYear);

      tweetRows.push({
        twitter_user_id: 0, //Elon
        text: text,
        tweeted_at: tweetedAt,
      });
    }

    //insert into db
    await db.insertInto('tweet').values(tweetRows).execute();

    console.log(`INSERTED ${tweetRows.length} tweets into DB successfully.`);
  }
  catch (error) {
    console.error('Error importing tweets:', error);
  } 
  finally {
    // Cleanly close the DB pool
    await db.destroy();
    // Also close the readline interface
    await rl.question('Press Enter to exit.');
    rl.close();
    process.exit(0);
  }
}

main().catch(async (err) => {
  console.error(err);
  await rl.question('Press Enter to exit.');
  process.exit(1);
});

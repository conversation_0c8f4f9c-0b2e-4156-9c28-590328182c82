//Configure these as needed
const destFilename = 'dbTypes.d.ts';
process.env.DATABASE_URL='postgres://postgres:test@localhost/PolyEnhanced';

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const sourcePath = path.join(__dirname, 'node_modules/kysely-codegen/dist/db.d.ts');
const targetDir = path.join(__dirname, 'src');

// Run kysely-codegen
exec('npx kysely-codegen', (error, stdout, stderr) => {
    if (error) {
        console.error(`Execution error: ${error}`);
        return;
    }
    console.log(stdout);
    console.error(stderr);

    // Move the file after codegen completes
    if (fs.existsSync(sourcePath)) {
        fs.rename(sourcePath, path.join(targetDir, destFilename), (err) => {
            if (err) throw err;
            console.log(`Moved ${sourcePath}\nto ${targetDir + "\\" + destFilename}\n`);
            
            modifyTypeFile();
        });
    } else {
        console.error(`File not found: ${sourcePath}`);
    }
});

//modifies the generated type file with some important things like 'type UserDTO = Selectable<User>' and so on
function modifyTypeFile() {
  const typesFilePath = targetDir + "\\" + destFilename;
  fs.readFile(typesFilePath, 'utf8', (err, fileContents) => {
    if (err) {
        console.error('Error reading file:', err);
        return;
    }

    // Extract the DB interface using a regular expression
    const match = /interface DB \{([\s\S]*?)\}/.exec(fileContents);

    if (match) {
        const lines = match[1].trim().split('\n');

        const dtoTypes = [];
        const dtoInterfaceLines = [];

        lines.forEach(line => {
          const typeName = line.split(':')[1].trim().slice(0, -1);
          dtoTypes.push(`export type ${typeName}DTO = Selectable<${typeName}>;`);
          dtoInterfaceLines.push(`  ${line.split(':')[0].trim()}: ${typeName}DTO;`);
        });

        const outputContent =
            `import { Selectable } from 'kysely';\r\n${fileContents}\r\n${dtoTypes.join('\r\n')}\r\n\r\nexport interface DBDTO {\r\n${dtoInterfaceLines.join('\r\n')}\r\n}`;

        
        //Write to disk
        fs.writeFile(typesFilePath, outputContent, 'utf8', (writeErr) => {
            if (writeErr) {
                console.error('Error writing file:', writeErr);
                return;
            }
            console.log('Selectable DTO types added to file.\n');
            
            done();
        });
    } else {
        console.error('DB interface not found in file.');
    }
  });
}

function done() {
  console.log('Succcess!\n');
}
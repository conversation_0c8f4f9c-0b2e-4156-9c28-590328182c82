/* eslint-env node */
/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */
// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js

/* eslint func-names: 0 */
/* eslint global-require: 0 */

const { configure } = require('quasar/wrappers');
const path = require('path');

module.exports = configure((ctx) => ({
  eslint: {
    // fix: true,
    // include: [],
    // exclude: [],
    // rawOptions: {},
    warnings: false,
    errors: false,
  },
  boot: [
    //'axios',
  ],
  css: [
    'app.scss',
  ],
  extras: [
    'roboto-font', // optional, you are not bound to it
    'material-icons', // optional, you are not bound to it
    'material-symbols-outlined',
  ],
  build: {
    env: {
      API_URL: ctx.dev ? 'localhost:3000' : '',
    },
    target: {
      browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
      //node: 'node16',
    },
    vueRouterMode: 'hash', // available values: 'hash', 'history'
    // Extend Vite configuration
    extendViteConf(viteConf) {
      viteConf.resolve = viteConf.resolve || {};
      viteConf.resolve.alias = {
        ...viteConf.resolve.alias,
        //'@ethereumjs/util': '@ethereumjs/util/dist/index.js',
        //'events': 'events/', // Use events polyfill (polymarket's clob-client uses metamask which uses @etherumjs/util which uses events which is NodeJS even though THEY SAID IT SUPPORTS BROWSERS)
        '@shared': path.resolve(__dirname, '../shared'),
        '@synced-validation': path.resolve(__dirname, './src/validation'),
        'validation': path.resolve(__dirname, './src/validation'),
        // ... other aliases as needed
      };
    }
  },
  devServer: {
    // https: true
    open: false, // opens browser window automatically
    proxy: {
      // proxy requests matching route to backend server
      '/oauth': {
        target: 'http://localhost:3000',
        changeOrigin: true
      },
    },
  },
  framework: {
    config: {
      loading: {
        delay: 400,
      },
    },
    plugins: ['Notify', 'Loading', 'Dialog'],
  },
  animations: [],
  ssr: {
    pwa: false,

    prodPort: 3000, // The default port that the production server should use
    // (gets superseded if process.env.PORT is specified at runtime)

    middlewares: [
      'render', // keep this as last one
    ],
  },
  pwa: {
    workboxMode: 'generateSW', // or 'injectManifest'
    injectPwaMetaTags: true,
    swFilename: 'sw.js',
    manifestFilename: 'manifest.json',
    useCredentialsForManifestTag: false,
  },
  cordova: {
    // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
  },
  capacitor: {
    hideSplashscreen: true,
  },
  electron: {
    inspectPort: 5858,
    bundler: 'packager', // 'packager' or 'builder'
    packager: {
    },

    builder: {
      appId: 'poly-enhanced-frontend',
    },
  },
  bex: {
    contentScripts: [
      'my-content-script',
    ],
  },
}));

import * as fs from 'fs';
import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcrypt';
import { PolyClient } from '@src/polymarket-api';

// Wraps an async function to catch any errors and pass them to the express error handler.
export function catchAsync(fn: (req: Request, res: Response, next: NextFunction) => Promise<void>) {
  return function (req: Request, res: Response, next: NextFunction) {
    fn(req, res, next).catch(next);
  };
};

export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export function saveConfig(config: any): void {
  console.log("Saving appconfig.json...");
  fs.writeFile('appconfig.json', JSON.stringify(config, null, 2), 'utf8', (err) => {
    if (err) throw err;
  });
};

const formatSizes: string[] = ['B', 'KB', 'MB', 'GB', 'TB'];

/**
 * Takes a formatted byte str "1.2 GB" and returns the number of bytes
 * @param {string} byteStr
 * @returns {number} number of bytes
 */
export function parseByteStr(byteStr: string): number {
  const [size, unit] = byteStr.split(' ');
  const sizeNum: number = parseFloat(size);
  const unitIndex: number = formatSizes.indexOf(unit);

  if (unitIndex === -1 || isNaN(sizeNum)) {
    throw new Error(`Invalid byte string ${byteStr}`);
  }

  return Math.round(sizeNum * Math.pow(1024, unitIndex));
};

export function hashPassword(password: string): string {
  const saltRounds = 10;
  const hash = bcrypt.hashSync(password, saltRounds);

  return hash;
}

export function emailToDisplayName(email: string): string {
  let displayName = email.split('@')[0];

  if (displayName.length < 4) {
      const neededChars = 4 - displayName.length;
      const randomNumber = Math.floor(Math.random() * Math.pow(10, neededChars)).toString();
      displayName += randomNumber;
  }

  return displayName;
}

export function enableAxiosProxy(enable: boolean)
{
  if (enable && config.polyProxy)
  {
    process.env.HTTP_PROXY = process.env.HTTPS_PROXY = config.polyProxy;
  }
  else
  {
    delete process.env.HTTP_PROXY;
    delete process.env.HTTPS_PROXY;
  }
}

let polyClient : PolyClient;
/**
 * Retrieves the global PolyClient instance populated by app config values.
 * @returns {PolyClient} Global PolyClient instance
 */
export function getPolyClient(): PolyClient {
  return polyClient ?? (polyClient = new PolyClient(config.privateKey, config.polyCredsApiKey, config.polyCredsSecret, config.polyCredsPassphrase));
}

import * as yup from 'yup';

export const isServer = true;

export interface ServerStringSchema extends yup.StringSchema {
  dbUnique(tableName: string, columnName: string, message?: string): yup.StringSchema;
}

export interface ClientStringSchema extends yup.StringSchema {
  [x: string]: any
}

yup.addMethod(yup.string, 'dbUnique', function (tableName, columnName, message) {
  return this.test('dbUnique', message, async function (value) {
    const { path, createError } = this;

    const exists = await global.db
      .selectFrom(tableName)
      .select(columnName)
      .where(columnName, '=', value)
      .executeTakeFirst();

    if (exists) {
      return createError({ path, message });
    }

    return true;
  });
});
const formatCache: Record<number, Intl.NumberFormat> = {};

export function formatCurrency(num: number, numDecimal: number = 2): string {
  let cache = formatCache[numDecimal];
  if (!cache) {
    cache = formatCache[numDecimal] = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: numDecimal,
      maximumFractionDigits: numDecimal,
    });
  }

  return cache.format(num);
};

export function formatCents(num: number, places: number = 2): string {
  return formatDecimal(num * 100, places, true) + "¢";
}

export function formatDecimal(num: number, places: number = 2, round: boolean = false) :string {
  const pow = Math.pow(10, places);
  const func = round ? Math.round : Math.floor;

  return (func(num * pow)/pow).toString();
}

export function roundDecimal(num: number, places: number = 2): number {
  const pow = Math.pow(10, places);
  return Math.round(num * pow) / pow;
}

export function isEmptyObj(obj: any): boolean {
  return Object.keys(obj).length === 0;
}

export function formatTimeAgoShort(date: Date, decimalPlaces: number = 1, now?: number): string {
  const diffMs = (now ?? Date.now()) - date.getTime();

  if (diffMs < 60_000) {
    const seconds = diffMs / 1_000;
    return `${seconds.toFixed(decimalPlaces)}s`;
  }
  else if (diffMs < 3_600_000) {
    const minutes = diffMs / 60_000;
    return `${minutes.toFixed(decimalPlaces)}m`;
  }
  else if (diffMs < 86_400_000) {
    const hours = diffMs / 3_600_000;
    return `${hours.toFixed(decimalPlaces)}h`;
  }
  else if (diffMs < 31_536_000_000) {
    const days = diffMs / 86_400_000;
    return `${days.toFixed(decimalPlaces)}d`;
  }
  else {
    const years = diffMs / 31_536_000_000;
    return `${years.toFixed(decimalPlaces)}y`;
  }
}

export function randomColorDistinct(totalColorsToGen: number, totalColorsAvail: number): string[] {
  const availCol: number[] = [];
  for (let i = 0; i < totalColorsAvail; i++) {
    availCol[i] = i;
  }

  const outCol: string[] = [];
  for (let i = 0; i < totalColorsToGen; i++) {
    const colInd = Math.floor((Math.random() * availCol.length));
    const color = availCol[colInd] * (360 / totalColorsAvail) % 360;
    outCol.push(`hsl(${color}, 100%, 50%)`);
    availCol.splice(colInd, 1);
  }

  return outCol;
}

export function randomColorByIndex(index: number, totalColors: number): string {
  return `hsl(${index * (360 / totalColors) % 360}, 100%, 50%)`;
}

export function filterMap<TArr, TNewArr>(arr: TArr[], callback: (item: TArr, index: number, array: TArr[]) => TNewArr | undefined): TNewArr[] {
  return arr.reduce<TNewArr[]>((acc, item, index) => {
    const result = callback(item, index, arr);
    if (result !== undefined) {
      acc.push(result);
    }
    return acc;
  }, []);
}

/**
* Only executes after `wait` ms delay. Resetting delay after each new call.
*
* @param func
* @param wait - The number of ms to delay
* @returns A debounced function
*/
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function debouncedFunction(this: any, ...args: Parameters<T>) {
    return new Promise<ReturnType<T>>((resolve) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        const result = func.apply(this, args);
        resolve(result);
      }, wait);
    });
  };
}

export function linkifyText(text: string): string {
  const urlRegex = /\bhttps?:\/\/[^\s()<>\[\]{}"']+[^.,)\]\s<>"']/g;
  return text.replace(urlRegex, (url) => {
    return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`;
  });
}

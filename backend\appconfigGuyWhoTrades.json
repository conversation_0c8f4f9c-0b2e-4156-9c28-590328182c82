{
  // Don't forget to escape backslashes for directories with \\
  "serverPort": 3000,
  "clientUrl": "http://localhost:9000",
  "url": "http://localhost:3000",
  "dbConnectionString": "postgres://postgres:test@localhost/PolyEnhanced",
  //"polyProxy": "*********************************************************",
  "polyProxy": "*********************************************************",
  "polyProxyWallet": "******************************************",
  "polyBaseWallet": "******************************************",
  "privateKey": "0xb7cf9059b764ba5682692d2431a1657e03259fbca23d59124cfc3f4de73f723b",
  "polyCredsApiKey": "3f2d7465-ae41-9996-377a-1f8ff55ffd72",
  "polyCredsSecret": "wi8yV5HcyPV4q4vNuoPj0T1ReFHW7Kup843J6GIppJc=",
  "polyCredsPassphrase": "****************************************************************",
  "polyClobSignatureType": 1,
  "polyClobApi": "https://clob.polymarket.com",
  "polyGammaApi": "https://gamma-api.polymarket.com",
  "polyDataApi": "https://data-api.polymarket.com",
  "tweetCounterPollRateMs": 60000,
}

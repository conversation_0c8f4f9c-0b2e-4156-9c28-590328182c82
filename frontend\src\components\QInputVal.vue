<template>
  <q-input
    v-bind="mergedAttrs"
    v-model="config.valueRef"
  />
</template>

<script setup lang="ts">
  import { computed, PropType, useAttrs } from 'vue';

  type ConfigType = {
    attrs: Record<string, unknown>;
    events: Record<string, Function>;
    valueRef: string | undefined;
  };

  const props = defineProps({
    config: {
      type: Object as PropType<ConfigType>,
      required: true
    }
  });

  const $attrs = useAttrs();

  const mergedAttrs = computed(() => {
    return {
      ...props.config.attrs,
      ...props.config.events,
      ...$attrs // Merges attributes and event listeners passed directly to the component
    };
  });
</script>

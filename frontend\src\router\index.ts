import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  LocationQueryRaw,
} from 'vue-router';

import routes from './routes';

//Setup the router
//Note: Function can be async if needed
export default route((/* { store, ssrContext } */) => {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory);

  const router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  //Prevent duplicate query params
  router.beforeEach((to, from, next) => {
    let newQuery: LocationQueryRaw | undefined;

    for (const key in to.query) {
      if (Array.isArray(to.query[key]))
      {
        if (!newQuery) {
          newQuery = { ...to.query };
        }

        newQuery[key] = to.query[key][0];
      }
    }

    //Redirect only if we fixed the query
    if (newQuery) {
      next({ path: to.path, query: newQuery, replace: true });
    }
    else {
      next();
    }
  });

  return router;
});

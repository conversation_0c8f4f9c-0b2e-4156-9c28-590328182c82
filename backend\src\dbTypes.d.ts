import { Selectable } from 'kysely';
import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface Appdata {
  appdata_id: Generated<number>;
  name: string;
  value: string | null;
}

export interface Market {
  condition_id: string;
  market_id: Generated<number>;
  slug: string | null;
}

export interface MarketHistory {
  asset_id: string;
  market_history_id: Generated<number>;
  market_id: number;
  price: number;
  shares: number;
  side: number;
  time: Timestamp;
  user_id: number | null;
  user_proxy_wallet: string;
}

export interface Polyuser {
  polyuser_id: Generated<number>;
  proxy_wallet: string;
}

export interface PolyuserHistory {
  asset_id: string;
  market_id: number;
  polyuser_history_id: Generated<number>;
  price: number;
  shares: number;
  side: number;
  time: Timestamp;
  user_id: number;
}

export interface Tweet {
  text: string;
  tweet_id: Generated<number>;
  tweeted_at: Timestamp;
  twitter_user_id: number;
  updated_at: Generated<Timestamp>;
}

export interface TwitterUser {
  display_name: string;
  twitter_handle: string;
  twitter_user_id: Generated<number>;
  updated_at: Generated<string>;
}

export interface DB {
  appdata: Appdata;
  market: Market;
  market_history: MarketHistory;
  polyuser: Polyuser;
  polyuser_history: PolyuserHistory;
  tweet: Tweet;
  twitter_user: TwitterUser;
}

export type AppdataDTO = Selectable<Appdata>;
export type MarketDTO = Selectable<Market>;
export type MarketHistoryDTO = Selectable<MarketHistory>;
export type PolyuserDTO = Selectable<Polyuser>;
export type PolyuserHistoryDTO = Selectable<PolyuserHistory>;
export type TweetDTO = Selectable<Tweet>;
export type TwitterUserDTO = Selectable<TwitterUser>;

export interface DBDTO {
  appdata: AppdataDTO;
  market: MarketDTO;
  market_history: MarketHistoryDTO;
  polyuser: PolyuserDTO;
  polyuser_history: PolyuserHistoryDTO;
  tweet: TweetDTO;
  twitter_user: TwitterUserDTO;
}
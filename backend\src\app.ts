declare global {
  var config: ConfigObject;
  var appData: AppData;
};

//Load config first as other app modules depend on it
import configLoader, { ConfigObject } from '@src/config-loader';
global.config = configLoader('./appconfig.json');

process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = (global.config.serverPort || 3000).toString();

//Import everything else
import path from 'path';
import express, { Request, Response, NextFunction } from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import logger from '@src/logger';
import dbInit from '@src/db';
import { validationExtensions } from '@src/validation/validation';
import { enableAxiosProxy } from '@src/utils';

import indexRouter from '@src/routes/index';
import eventsRouter from '@src/routes/events';
import polyUserRouter from '@src/routes/polyuser';
import commentsRouter from '@src/routes/comments';
import startTweetCounterService from '@src/services/tweet-counter';
import { AppData } from '@src/models/appdata-model';
import { AxiosError } from 'axios';

dbInit();
enableAxiosProxy(true);

startTweetCounterService(config.tweetCounterPollRateMs);

//Setup app
const app = express();
app.use(logger);
//DEV: to enable Vue dev server communication
app.use(cors({
  origin: function (origin, callback) {
    //Over comes issue with "*" origin which isn't allowed with credentials
    callback(null, true);
  },
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(validationExtensions());
//Static file dir
app.use(express.static(path.join(__dirname, 'public')));

//DEBUG simulate network lag
// app.use((req, res, next) => {
//   const [maxMs, minMs] = [2000, 1000];
//   const delay = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
//   setTimeout(next, delay);
// });

//Setup routes
app.use('/', indexRouter);
app.use('/events', eventsRouter);
app.use('/polyuser', polyUserRouter);
app.use('/comments', commentsRouter);

//Global error handler
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  //DEBUG
  console.error("G", err);

  //Send axios status and error data if possible
  if (err instanceof AxiosError && err.response) {
    res.status(err.response.status).send(err.response.data);
  }
  else {
    res.status(500).send({ error: err.message });
  }
});

//Shutdown events for graceful termination
const processShutdown = async () => {
  console.log('Shutting down server...');
  await global.connectionPool.end();
  console.log('Exiting...');
  process.exit(0);
};
process.on('SIGINT', processShutdown);
process.on('SIGTERM', processShutdown);

//Initialize stuff (probably not the best way as theres a small window where server is up but this hasn't finished)
(async () => {
  global.appData = await global.appDataModel.getAppData();
})();

console.log(`Server started (${process.env.NODE_ENV}, port ${process.env.PORT}, dir ${__dirname})`);

export { app };
export default app;
module.exports = app;
